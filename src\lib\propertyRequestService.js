import { supabase } from "./supabase";

/**
 * Property Request Service
 * Handles all property request related operations
 */
export const propertyRequestService = {
  /**
   * Create a new property request
   * @param {Object} requestData - Property request data
   * @returns {Promise<{data: Object|null, error: Error|null}>}
   */
  async createPropertyRequest(requestData) {
    try {
      // Validate required fields
      const requiredFields = [
        "name",
        "email",
        "property_type",
        "sale_type",
        "location_preference",
      ];
      for (const field of requiredFields) {
        if (!requestData[field]) {
          throw new Error(`${field} is required`);
        }
      }

      // Sanitize and prepare data
      const sanitizedData = {
        name: requestData.name.trim(),
        email: requestData.email.trim().toLowerCase(),
        phone: requestData.phone?.trim() || null,
        property_type: requestData.property_type,
        sale_type: requestData.sale_type,
        location_preference: requestData.location_preference.trim(),
        min_budget: requestData.min_budget
          ? parseFloat(requestData.min_budget)
          : null,
        max_budget: requestData.max_budget
          ? parseFloat(requestData.max_budget)
          : null,
        min_bedrooms: requestData.min_bedrooms
          ? parseInt(requestData.min_bedrooms)
          : 0,
        max_bedrooms: requestData.max_bedrooms
          ? parseInt(requestData.max_bedrooms)
          : null,
        min_bathrooms: requestData.min_bathrooms
          ? parseInt(requestData.min_bathrooms)
          : 0,
        max_bathrooms: requestData.max_bathrooms
          ? parseInt(requestData.max_bathrooms)
          : null,
        min_square_feet: requestData.min_square_feet
          ? parseInt(requestData.min_square_feet)
          : null,
        max_square_feet: requestData.max_square_feet
          ? parseInt(requestData.max_square_feet)
          : null,
        specific_features: requestData.specific_features || [],
        additional_requirements:
          requestData.additional_requirements?.trim() || null,
        status: "new",
        priority: "normal",
      };

      // Validate budget range
      if (
        sanitizedData.min_budget &&
        sanitizedData.max_budget &&
        sanitizedData.min_budget > sanitizedData.max_budget
      ) {
        throw new Error("Minimum budget cannot be greater than maximum budget");
      }

      // Validate bedroom range
      if (
        sanitizedData.min_bedrooms &&
        sanitizedData.max_bedrooms &&
        sanitizedData.min_bedrooms > sanitizedData.max_bedrooms
      ) {
        throw new Error(
          "Minimum bedrooms cannot be greater than maximum bedrooms"
        );
      }

      // Validate bathroom range
      if (
        sanitizedData.min_bathrooms &&
        sanitizedData.max_bathrooms &&
        sanitizedData.min_bathrooms > sanitizedData.max_bathrooms
      ) {
        throw new Error(
          "Minimum bathrooms cannot be greater than maximum bathrooms"
        );
      }

      // Validate square feet range
      if (
        sanitizedData.min_square_feet &&
        sanitizedData.max_square_feet &&
        sanitizedData.min_square_feet > sanitizedData.max_square_feet
      ) {
        throw new Error(
          "Minimum square feet cannot be greater than maximum square feet"
        );
      }

      const { data, error } = await supabase
        .from("property_requests")
        .insert([sanitizedData])
        .select()
        .single();

      if (error) throw error;

      // Send email notification to admin (fire and forget)
      try {
        await supabase.functions.invoke("send-property-request-notification", {
          body: { requestId: data.id },
        });
      } catch (emailError) {
        // Log email error but don't fail the request creation
        console.warn("Failed to send email notification:", emailError);
      }

      return { data, error: null };
    } catch (error) {
      console.error("Error creating property request:", error);
      return { data: null, error };
    }
  },

  /**
   * Get property requests with pagination and filtering (Admin only)
   * @param {Object} options - Query options
   * @returns {Promise<{data: Array|null, error: Error|null}>}
   */
  async getPropertyRequests(options = {}) {
    try {
      const {
        limit = 20,
        offset = 0,
        status = null,
        property_type = null,
        sale_type = null,
        search = null,
      } = options;

      const { data, error } = await supabase.rpc("get_property_requests", {
        p_limit: limit,
        p_offset: offset,
        p_status: status,
        p_property_type: property_type,
        p_sale_type: sale_type,
        p_search: search,
      });

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      console.error("Error fetching property requests:", error);
      return { data: null, error };
    }
  },

  /**
   * Get property request by ID (Admin only)
   * @param {string} id - Property request ID
   * @returns {Promise<{data: Object|null, error: Error|null}>}
   */
  async getPropertyRequestById(id) {
    try {
      const { data, error } = await supabase
        .from("property_requests")
        .select(
          `
          *,
          assigned_admin:assigned_admin_id(id, email)
        `
        )
        .eq("id", id)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      console.error("Error fetching property request:", error);
      return { data: null, error };
    }
  },

  /**
   * Update property request status (Admin only)
   * @param {string} id - Property request ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<{data: boolean|null, error: Error|null}>}
   */
  async updatePropertyRequestStatus(id, updates) {
    try {
      const { status, admin_notes, assigned_admin_id } = updates;

      const { data, error } = await supabase.rpc(
        "update_property_request_status",
        {
          p_request_id: id,
          p_status: status,
          p_admin_notes: admin_notes,
          p_assigned_admin_id: assigned_admin_id,
        }
      );

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      console.error("Error updating property request status:", error);
      return { data: null, error };
    }
  },

  /**
   * Delete property request (Admin only)
   * @param {string} id - Property request ID
   * @returns {Promise<{data: boolean|null, error: Error|null}>}
   */
  async deletePropertyRequest(id) {
    try {
      const { error } = await supabase
        .from("property_requests")
        .delete()
        .eq("id", id);

      if (error) throw error;

      return { data: true, error: null };
    } catch (error) {
      console.error("Error deleting property request:", error);
      return { data: null, error };
    }
  },

  /**
   * Get property request statistics (Admin only)
   * @returns {Promise<{data: Object|null, error: Error|null}>}
   */
  async getPropertyRequestStats() {
    try {
      const { data, error } = await supabase.rpc("get_property_request_stats");

      if (error) throw error;

      return { data: data[0] || null, error: null };
    } catch (error) {
      console.error("Error fetching property request stats:", error);
      return { data: null, error };
    }
  },

  /**
   * Subscribe to property request changes (Admin only)
   * @param {Function} callback - Callback function for changes
   * @returns {Object} Subscription object
   */
  subscribeToPropertyRequests(callback) {
    return supabase
      .channel("property_requests_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "property_requests",
        },
        callback
      )
      .subscribe();
  },

  /**
   * Get property types for form options
   * @returns {Array} Property type options
   */
  getPropertyTypeOptions() {
    return [
      { value: "house", label: "House" },
      { value: "apartment", label: "Apartment" },
      { value: "condo", label: "Condo" },
      { value: "townhouse", label: "Townhouse" },
      { value: "land", label: "Land" },
      { value: "commercial", label: "Commercial" },
      { value: "other", label: "Other" },
    ];
  },

  /**
   * Get sale types for form options
   * @returns {Array} Sale type options
   */
  getSaleTypeOptions() {
    return [
      { value: "buy", label: "Buy" },
      { value: "rent", label: "Rent" },
    ];
  },

  /**
   * Get status options for admin
   * @returns {Array} Status options
   */
  getStatusOptions() {
    return [
      { value: "new", label: "New", color: "blue" },
      { value: "in_progress", label: "In Progress", color: "yellow" },
      { value: "resolved", label: "Resolved", color: "green" },
      { value: "closed", label: "Closed", color: "gray" },
    ];
  },

  /**
   * Get priority options for admin
   * @returns {Array} Priority options
   */
  getPriorityOptions() {
    return [
      { value: "low", label: "Low", color: "gray" },
      { value: "normal", label: "Normal", color: "blue" },
      { value: "high", label: "High", color: "orange" },
      { value: "urgent", label: "Urgent", color: "red" },
    ];
  },

  /**
   * Format budget for display
   * @param {number} amount - Budget amount
   * @returns {string} Formatted budget
   */
  formatBudget(amount) {
    if (!amount) return "Not specified";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  },

  /**
   * Format budget range for display
   * @param {number} min - Minimum budget
   * @param {number} max - Maximum budget
   * @returns {string} Formatted budget range
   */
  formatBudgetRange(min, max) {
    if (!min && !max) return "Not specified";
    if (!min) return `Up to ${this.formatBudget(max)}`;
    if (!max) return `From ${this.formatBudget(min)}`;
    return `${this.formatBudget(min)} - ${this.formatBudget(max)}`;
  },
};
