import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, UserIcon, HomeIcon, CurrencyDollarIcon, MapPinIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { propertyRequestService } from '../../../lib/propertyRequestService';

/**
 * Property Request Detail Modal Component
 * Shows detailed view of a property request with admin actions
 */
const PropertyRequestDetailModal = ({ request, isOpen, onClose, onUpdate }) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [adminNotes, setAdminNotes] = useState(request?.admin_notes || '');
  const [status, setStatus] = useState(request?.status || 'new');

  const statusOptions = propertyRequestService.getStatusOptions();
  const propertyTypeOptions = propertyRequestService.getPropertyTypeOptions();
  const saleTypeOptions = propertyRequestService.getSaleTypeOptions();

  const handleUpdate = async () => {
    if (!request) return;

    setIsUpdating(true);
    try {
      const { error } = await propertyRequestService.updatePropertyRequestStatus(request.id, {
        status,
        admin_notes: adminNotes
      });

      if (error) throw error;

      onUpdate?.();
      onClose();
    } catch (err) {
      console.error('Error updating request:', err);
      alert('Failed to update request. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusConfig = statusOptions.find(s => s.value === status);
    if (!statusConfig) return null;

    const colorClasses = {
      blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
      green: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      gray: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${colorClasses[statusConfig.color]}`}>
        {statusConfig.label}
      </span>
    );
  };

  if (!request) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-brown-dark/80 backdrop-blur-sm"
          onClick={(e) => e.target === e.currentTarget && onClose()}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="bg-white dark:bg-brown-dark rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-brown/10 dark:border-beige-medium/10">
              <div>
                <h2 className="text-2xl font-heading font-bold text-brown-dark dark:text-beige-light">
                  Property Request Details
                </h2>
                <p className="text-brown dark:text-beige-medium mt-1">
                  Submitted on {formatDate(request.created_at)}
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-brown dark:text-beige-medium hover:text-brown-dark dark:hover:text-beige-light hover:bg-brown/5 dark:hover:bg-beige-medium/5 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)] space-y-6">
              {/* Contact Information */}
              <div className="bg-beige-light dark:bg-brown/20 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <UserIcon className="h-5 w-5 text-taupe" />
                  <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
                    Contact Information
                  </h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                      Name
                    </label>
                    <p className="text-brown-dark dark:text-beige-light">{request.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                      Email
                    </label>
                    <p className="text-brown-dark dark:text-beige-light">
                      <a href={`mailto:${request.email}`} className="text-taupe hover:underline">
                        {request.email}
                      </a>
                    </p>
                  </div>
                  {request.phone && (
                    <div>
                      <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                        Phone
                      </label>
                      <p className="text-brown-dark dark:text-beige-light">
                        <a href={`tel:${request.phone}`} className="text-taupe hover:underline">
                          {request.phone}
                        </a>
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Property Requirements */}
              <div className="bg-beige-light dark:bg-brown/20 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <HomeIcon className="h-5 w-5 text-taupe" />
                  <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
                    Property Requirements
                  </h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                      Property Type
                    </label>
                    <p className="text-brown-dark dark:text-beige-light">
                      {propertyTypeOptions.find(t => t.value === request.property_type)?.label}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                      Looking to
                    </label>
                    <p className="text-brown-dark dark:text-beige-light">
                      {saleTypeOptions.find(t => t.value === request.sale_type)?.label}
                    </p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                      Location Preference
                    </label>
                    <p className="text-brown-dark dark:text-beige-light">{request.location_preference}</p>
                  </div>
                </div>
              </div>

              {/* Budget and Size */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Budget */}
                <div className="bg-beige-light dark:bg-brown/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <CurrencyDollarIcon className="h-5 w-5 text-taupe" />
                    <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
                      Budget Range
                    </h3>
                  </div>
                  <p className="text-brown-dark dark:text-beige-light">
                    {propertyRequestService.formatBudgetRange(request.min_budget, request.max_budget)}
                  </p>
                </div>

                {/* Size Requirements */}
                <div className="bg-beige-light dark:bg-brown/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <HomeIcon className="h-5 w-5 text-taupe" />
                    <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
                      Size Requirements
                    </h3>
                  </div>
                  <div className="space-y-2 text-sm">
                    {(request.min_bedrooms || request.max_bedrooms) && (
                      <p className="text-brown-dark dark:text-beige-light">
                        Bedrooms: {request.min_bedrooms || 0} - {request.max_bedrooms || '∞'}
                      </p>
                    )}
                    {(request.min_bathrooms || request.max_bathrooms) && (
                      <p className="text-brown-dark dark:text-beige-light">
                        Bathrooms: {request.min_bathrooms || 0} - {request.max_bathrooms || '∞'}
                      </p>
                    )}
                    {(request.min_square_feet || request.max_square_feet) && (
                      <p className="text-brown-dark dark:text-beige-light">
                        Square Feet: {request.min_square_feet || 0} - {request.max_square_feet || '∞'}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Features and Additional Requirements */}
              {(request.specific_features?.length > 0 || request.additional_requirements) && (
                <div className="bg-beige-light dark:bg-brown/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <ChatBubbleLeftRightIcon className="h-5 w-5 text-taupe" />
                    <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
                      Additional Details
                    </h3>
                  </div>
                  
                  {request.specific_features?.length > 0 && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-2">
                        Desired Features
                      </label>
                      <div className="flex flex-wrap gap-2">
                        {request.specific_features.map((feature, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-taupe/10 text-taupe"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {request.additional_requirements && (
                    <div>
                      <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-2">
                        Additional Requirements
                      </label>
                      <p className="text-brown-dark dark:text-beige-light whitespace-pre-wrap">
                        {request.additional_requirements}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Admin Actions */}
              <div className="bg-beige-light dark:bg-brown/20 rounded-lg p-4">
                <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light mb-4">
                  Admin Actions
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                      Status
                    </label>
                    <select
                      value={status}
                      onChange={(e) => setStatus(e.target.value)}
                      className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
                    >
                      {statusOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                      Current Status
                    </label>
                    <div className="pt-2">
                      {getStatusBadge(request.status)}
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-brown dark:text-beige-medium mb-1">
                    Admin Notes
                  </label>
                  <textarea
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors resize-vertical"
                    placeholder="Add notes about this request..."
                  />
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-brown/10 dark:border-beige-medium/10">
              <button
                onClick={onClose}
                className="px-6 py-2 border border-brown/20 dark:border-beige-medium/20 text-brown dark:text-beige-medium rounded-lg hover:bg-brown/5 dark:hover:bg-beige-medium/5 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleUpdate}
                disabled={isUpdating}
                className="px-6 py-2 bg-taupe text-white rounded-lg hover:bg-taupe/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
              >
                {isUpdating ? (
                  <>
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Updating...
                  </>
                ) : (
                  'Update Request'
                )}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PropertyRequestDetailModal;
