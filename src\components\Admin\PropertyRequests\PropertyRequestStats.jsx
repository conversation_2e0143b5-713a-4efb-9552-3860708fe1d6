import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { propertyRequestService } from "../../../lib/propertyRequestService";
import {
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
} from "@heroicons/react/24/outline";
import { FaArrowTrendUp } from "react-icons/fa6";

/**
 * Property Request Statistics Component
 * Shows overview statistics for property requests in admin dashboard
 */
const PropertyRequestStats = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } =
        await propertyRequestService.getPropertyRequestStats();

      if (error) throw error;

      setStats(data);
    } catch (err) {
      console.error("Error fetching property request stats:", err);
      setError(err.message || "Failed to fetch statistics");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div
            key={i}
            className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6"
          >
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-brown rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 dark:bg-brown rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <button
            onClick={fetchStats}
            className="px-4 py-2 bg-taupe text-white rounded-lg hover:bg-taupe/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const statCards = [
    {
      title: "Total Requests",
      value: stats.total_requests || 0,
      icon: DocumentTextIcon,
      color: "blue",
      description: "All time requests",
    },
    {
      title: "New Requests",
      value: stats.new_requests || 0,
      icon: ClockIcon,
      color: "yellow",
      description: "Awaiting review",
    },
    {
      title: "In Progress",
      value: stats.in_progress_requests || 0,
      icon: FaArrowTrendUp,
      color: "orange",
      description: "Being processed",
    },
    {
      title: "Resolved",
      value: stats.resolved_requests || 0,
      icon: CheckCircleIcon,
      color: "green",
      description: "Successfully completed",
    },
    {
      title: "Urgent Requests",
      value: stats.urgent_requests || 0,
      icon: ExclamationTriangleIcon,
      color: "red",
      description: "High priority items",
    },
    {
      title: "This Week",
      value: stats.requests_this_week || 0,
      icon: CalendarIcon,
      color: "purple",
      description: "New this week",
    },
    {
      title: "This Month",
      value: stats.requests_this_month || 0,
      icon: CalendarIcon,
      color: "indigo",
      description: "New this month",
    },
    {
      title: "Closed",
      value: stats.closed_requests || 0,
      icon: DocumentTextIcon,
      color: "gray",
      description: "Archived requests",
    },
  ];

  const getColorClasses = (color) => {
    const colorMap = {
      blue: {
        bg: "bg-blue-50 dark:bg-blue-900/20",
        icon: "text-blue-600 dark:text-blue-400",
        text: "text-blue-900 dark:text-blue-100",
      },
      yellow: {
        bg: "bg-yellow-50 dark:bg-yellow-900/20",
        icon: "text-yellow-600 dark:text-yellow-400",
        text: "text-yellow-900 dark:text-yellow-100",
      },
      orange: {
        bg: "bg-orange-50 dark:bg-orange-900/20",
        icon: "text-orange-600 dark:text-orange-400",
        text: "text-orange-900 dark:text-orange-100",
      },
      green: {
        bg: "bg-green-50 dark:bg-green-900/20",
        icon: "text-green-600 dark:text-green-400",
        text: "text-green-900 dark:text-green-100",
      },
      red: {
        bg: "bg-red-50 dark:bg-red-900/20",
        icon: "text-red-600 dark:text-red-400",
        text: "text-red-900 dark:text-red-100",
      },
      purple: {
        bg: "bg-purple-50 dark:bg-purple-900/20",
        icon: "text-purple-600 dark:text-purple-400",
        text: "text-purple-900 dark:text-purple-100",
      },
      indigo: {
        bg: "bg-indigo-50 dark:bg-indigo-900/20",
        icon: "text-indigo-600 dark:text-indigo-400",
        text: "text-indigo-900 dark:text-indigo-100",
      },
      gray: {
        bg: "bg-gray-50 dark:bg-gray-900/20",
        icon: "text-gray-600 dark:text-gray-400",
        text: "text-gray-900 dark:text-gray-100",
      },
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => {
        const colors = getColorClasses(stat.color);
        const Icon = stat.icon;

        return (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-brown dark:text-beige-medium mb-1">
                  {stat.title}
                </p>
                <p className="text-3xl font-bold text-brown-dark dark:text-beige-light">
                  {stat.value.toLocaleString()}
                </p>
                <p className="text-xs text-brown dark:text-beige-medium mt-1">
                  {stat.description}
                </p>
              </div>
              <div className={`p-3 rounded-lg ${colors.bg}`}>
                <Icon className={`h-6 w-6 ${colors.icon}`} />
              </div>
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};

export default PropertyRequestStats;
