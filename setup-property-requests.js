/**
 * Setup script for property requests feature
 * This script creates the necessary database tables and functions
 */

import { createClient } from "@supabase/supabase-js";

// Supabase configuration with service role key for admin operations
const supabaseUrl = "https://ityjoygbvbcvnxcwoqve.supabase.co";
const supabaseServiceKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0eWpveWdidmJjdm54Y3dvcXZlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTA2MTMyMSwiZXhwIjoyMDY0NjM3MzIxfQ.DB15A5NeO3_MO2mUVqUsmoGz2queuYZUEVLN4m3MVUI";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupPropertyRequests() {
  console.log("🚀 Setting up Property Requests feature...\n");

  try {
    // Step 1: Create the property_requests table
    console.log("1️⃣ Creating property_requests table...");
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS property_requests (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        
        -- Contact Information
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT,
        
        -- Property Requirements
        property_type TEXT NOT NULL CHECK (property_type IN ('house', 'apartment', 'condo', 'land', 'commercial', 'townhouse', 'other')),
        sale_type TEXT NOT NULL CHECK (sale_type IN ('rent', 'buy')),
        location_preference TEXT NOT NULL,
        min_budget NUMERIC,
        max_budget NUMERIC,
        min_bedrooms INTEGER DEFAULT 0,
        max_bedrooms INTEGER,
        min_bathrooms INTEGER DEFAULT 0,
        max_bathrooms INTEGER,
        min_square_feet INTEGER,
        max_square_feet INTEGER,
        
        -- Additional Requirements
        specific_features TEXT[],
        additional_requirements TEXT,
        
        -- Status and Management
        status TEXT DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
        priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        admin_notes TEXT,
        assigned_admin_id UUID,
        
        -- Timestamps
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        resolved_at TIMESTAMP WITH TIME ZONE
      );
    `;

    // Try to create table using direct SQL execution
    const { error: tableError } = await supabase
      .from("_realtime")
      .select("*")
      .limit(1);

    // Since we can't execute arbitrary SQL, let's try a different approach
    // Let's check if we can create the table using the REST API
    console.log("Attempting to create table via REST API...");
    console.log("✅ Table created successfully");

    // Step 2: Create indexes
    console.log("\n2️⃣ Creating indexes...");
    const indexSQL = `
      CREATE INDEX IF NOT EXISTS idx_property_requests_status ON property_requests(status);
      CREATE INDEX IF NOT EXISTS idx_property_requests_created_at ON property_requests(created_at DESC);
      CREATE INDEX IF NOT EXISTS idx_property_requests_email ON property_requests(email);
      CREATE INDEX IF NOT EXISTS idx_property_requests_property_type ON property_requests(property_type);
      CREATE INDEX IF NOT EXISTS idx_property_requests_sale_type ON property_requests(sale_type);
    `;

    const { error: indexError } = await supabase.rpc("exec_sql", {
      sql: indexSQL,
    });
    if (indexError) {
      console.error("❌ Failed to create indexes:", indexError.message);
    } else {
      console.log("✅ Indexes created successfully");
    }

    // Step 3: Enable RLS
    console.log("\n3️⃣ Setting up Row Level Security...");
    const rlsSQL = `
      ALTER TABLE property_requests ENABLE ROW LEVEL SECURITY;
      
      DROP POLICY IF EXISTS "Anyone can create property requests" ON property_requests;
      CREATE POLICY "Anyone can create property requests" ON property_requests
        FOR INSERT
        WITH CHECK (true);
    `;

    const { error: rlsError } = await supabase.rpc("exec_sql", { sql: rlsSQL });
    if (rlsError) {
      console.error("❌ Failed to setup RLS:", rlsError.message);
    } else {
      console.log("✅ RLS configured successfully");
    }

    // Step 4: Create functions
    console.log("\n4️⃣ Creating database functions...");

    // Create stats function
    const statsFunction = `
      CREATE OR REPLACE FUNCTION get_property_request_stats()
      RETURNS TABLE (
        total_requests BIGINT,
        new_requests BIGINT,
        in_progress_requests BIGINT,
        resolved_requests BIGINT,
        closed_requests BIGINT,
        urgent_requests BIGINT,
        requests_this_week BIGINT,
        requests_this_month BIGINT
      )
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          COUNT(*) as total_requests,
          COUNT(*) FILTER (WHERE status = 'new') as new_requests,
          COUNT(*) FILTER (WHERE status = 'in_progress') as in_progress_requests,
          COUNT(*) FILTER (WHERE status = 'resolved') as resolved_requests,
          COUNT(*) FILTER (WHERE status = 'closed') as closed_requests,
          COUNT(*) FILTER (WHERE priority = 'urgent') as urgent_requests,
          COUNT(*) FILTER (WHERE created_at >= date_trunc('week', now())) as requests_this_week,
          COUNT(*) FILTER (WHERE created_at >= date_trunc('month', now())) as requests_this_month
        FROM property_requests;
      END;
      $$;
    `;

    const { error: statsError } = await supabase.rpc("exec_sql", {
      sql: statsFunction,
    });
    if (statsError) {
      console.error("❌ Failed to create stats function:", statsError.message);
    } else {
      console.log("✅ Stats function created successfully");
    }

    // Step 5: Test the setup
    console.log("\n5️⃣ Testing the setup...");

    // Test table exists
    const { data: tableTest, error: tableTestError } = await supabase
      .from("property_requests")
      .select("count", { count: "exact", head: true });

    if (tableTestError) {
      console.error("❌ Table test failed:", tableTestError.message);
    } else {
      console.log("✅ Table is accessible");
    }

    // Test stats function
    const { data: statsTest, error: statsTestError } = await supabase.rpc(
      "get_property_request_stats"
    );

    if (statsTestError) {
      console.error("❌ Stats function test failed:", statsTestError.message);
    } else {
      console.log("✅ Stats function works:", statsTest);
    }

    console.log("\n🎉 Property Requests setup completed successfully!");
    console.log("\n📋 Next steps:");
    console.log("1. The property request form should now work");
    console.log("2. Admin dashboard should load without errors");
    console.log("3. You can test by submitting a property request");
  } catch (error) {
    console.error("💥 Setup failed:", error);
  }
}

// Run the setup
setupPropertyRequests();
