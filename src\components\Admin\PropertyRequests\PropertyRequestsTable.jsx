import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { propertyRequestService } from "../../../lib/propertyRequestService";
import PropertyRequestDetailModal from "./PropertyRequestDetailModal";
import {
  EyeIcon,
  PencilSquareIcon,
  TrashIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

/**
 * Property Requests Table Component
 * Displays and manages property requests in the admin dashboard
 */
const PropertyRequestsTable = () => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: "",
    property_type: "",
    sale_type: "",
    search: "",
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10,
  });

  const statusOptions = propertyRequestService.getStatusOptions();
  const propertyTypeOptions = propertyRequestService.getPropertyTypeOptions();
  const saleTypeOptions = propertyRequestService.getSaleTypeOptions();

  // Fetch property requests
  const fetchRequests = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await propertyRequestService.getPropertyRequests({
        limit: pagination.limit,
        offset: (pagination.currentPage - 1) * pagination.limit,
        status: filters.status || null,
        property_type: filters.property_type || null,
        sale_type: filters.sale_type || null,
        search: filters.search || null,
      });

      if (error) throw error;

      if (data && data.length > 0) {
        setRequests(data);
        setPagination((prev) => ({
          ...prev,
          totalCount: data[0].total_count,
          totalPages: Math.ceil(data[0].total_count / pagination.limit),
        }));
      } else {
        setRequests([]);
        setPagination((prev) => ({
          ...prev,
          totalCount: 0,
          totalPages: 1,
        }));
      }
    } catch (err) {
      console.error("Error fetching property requests:", err);
      setError(err.message || "Failed to fetch property requests");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [pagination.currentPage, filters]);

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (page) => {
    setPagination((prev) => ({ ...prev, currentPage: page }));
  };

  const handleStatusUpdate = async (requestId, newStatus, adminNotes = "") => {
    try {
      const { error } =
        await propertyRequestService.updatePropertyRequestStatus(requestId, {
          status: newStatus,
          admin_notes: adminNotes,
        });

      if (error) throw error;

      // Refresh the requests list
      fetchRequests();
    } catch (err) {
      console.error("Error updating request status:", err);
      setError(err.message || "Failed to update request status");
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = statusOptions.find((s) => s.value === status);
    if (!statusConfig) return null;

    const colorClasses = {
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
      green:
        "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          colorClasses[statusConfig.color]
        }`}
      >
        {statusConfig.label}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 dark:bg-brown rounded w-1/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="h-12 bg-gray-200 dark:bg-brown rounded"
              ></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
        <div className="text-center py-8">
          <p className="text-red-600 dark:text-red-400">{error}</p>
          <button
            onClick={fetchRequests}
            className="mt-4 px-4 py-2 bg-taupe text-white rounded-lg hover:bg-taupe/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-brown">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
              Property Requests
            </h3>
            <p className="text-brown dark:text-beige-medium text-sm">
              {pagination.totalCount} total requests
            </p>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 text-brown dark:text-beige-medium hover:text-brown-dark dark:hover:text-beige-light border border-brown/20 dark:border-beige-medium/20 rounded-lg hover:bg-brown/5 dark:hover:bg-beige-medium/5 transition-colors"
            >
              <FunnelIcon className="h-4 w-4" />
              Filters
            </button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"
          >
            <div>
              <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
                Search
              </label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-brown dark:text-beige-medium" />
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  placeholder="Search requests..."
                  className="w-full pl-10 pr-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange("status", e.target.value)}
                className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              >
                <option value="">All Statuses</option>
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
                Property Type
              </label>
              <select
                value={filters.property_type}
                onChange={(e) =>
                  handleFilterChange("property_type", e.target.value)
                }
                className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              >
                <option value="">All Types</option>
                {propertyTypeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
                Sale Type
              </label>
              <select
                value={filters.sale_type}
                onChange={(e) =>
                  handleFilterChange("sale_type", e.target.value)
                }
                className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              >
                <option value="">All Sale Types</option>
                {saleTypeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </motion.div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 dark:bg-brown/20">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-brown dark:text-beige-medium uppercase tracking-wider">
                Contact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-brown dark:text-beige-medium uppercase tracking-wider">
                Requirements
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-brown dark:text-beige-medium uppercase tracking-wider">
                Budget
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-brown dark:text-beige-medium uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-brown dark:text-beige-medium uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-brown dark:text-beige-medium uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-brown">
            {requests.map((request) => (
              <tr
                key={request.id}
                className="hover:bg-gray-50 dark:hover:bg-brown/10"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-brown-dark dark:text-beige-light">
                      {request.name}
                    </div>
                    <div className="text-sm text-brown dark:text-beige-medium">
                      {request.email}
                    </div>
                    {request.phone && (
                      <div className="text-sm text-brown dark:text-beige-medium">
                        {request.phone}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-brown-dark dark:text-beige-light">
                    {
                      propertyTypeOptions.find(
                        (t) => t.value === request.property_type
                      )?.label
                    }{" "}
                    •{" "}
                    {
                      saleTypeOptions.find((t) => t.value === request.sale_type)
                        ?.label
                    }
                  </div>
                  <div className="text-sm text-brown dark:text-beige-medium">
                    {request.location_preference}
                  </div>
                  {(request.min_bedrooms || request.max_bedrooms) && (
                    <div className="text-sm text-brown dark:text-beige-medium">
                      {request.min_bedrooms || 0}-{request.max_bedrooms || "∞"}{" "}
                      bedrooms
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-brown-dark dark:text-beige-light">
                    {propertyRequestService.formatBudgetRange(
                      request.min_budget,
                      request.max_budget
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(request.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-brown dark:text-beige-medium">
                  {formatDate(request.created_at)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => {
                        setSelectedRequest(request);
                        setShowDetailModal(true);
                      }}
                      className="text-taupe hover:text-taupe/80 transition-colors"
                      title="View details"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <select
                      value={request.status}
                      onChange={(e) =>
                        handleStatusUpdate(request.id, e.target.value)
                      }
                      className="text-xs border border-brown/20 dark:border-beige-medium/20 rounded px-2 py-1 bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light"
                    >
                      {statusOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Empty State */}
      {requests.length === 0 && (
        <div className="text-center py-12">
          <p className="text-brown dark:text-beige-medium">
            No property requests found.
          </p>
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200 dark:border-brown">
          <div className="flex items-center justify-between">
            <div className="text-sm text-brown dark:text-beige-medium">
              Showing {(pagination.currentPage - 1) * pagination.limit + 1} to{" "}
              {Math.min(
                pagination.currentPage * pagination.limit,
                pagination.totalCount
              )}{" "}
              of {pagination.totalCount} results
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1}
                className="p-2 text-brown dark:text-beige-medium hover:text-brown-dark dark:hover:text-beige-light disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>
              <span className="text-sm text-brown-dark dark:text-beige-light">
                {pagination.currentPage} of {pagination.totalPages}
              </span>
              <button
                onClick={() => handlePageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === pagination.totalPages}
                className="p-2 text-brown dark:text-beige-medium hover:text-brown-dark dark:hover:text-beige-light disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Detail Modal */}
      <PropertyRequestDetailModal
        request={selectedRequest}
        isOpen={showDetailModal}
        onClose={() => {
          setShowDetailModal(false);
          setSelectedRequest(null);
        }}
        onUpdate={fetchRequests}
      />
    </div>
  );
};

export default PropertyRequestsTable;
