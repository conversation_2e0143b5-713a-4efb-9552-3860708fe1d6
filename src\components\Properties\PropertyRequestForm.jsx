import { useState } from "react";
import { useForm } from "react-hook-form";
import { motion } from "framer-motion";
import { propertyRequestService } from "../../lib/propertyRequestService";
import {
  validatePropertyRequestForm,
  getErrorMessage,
} from "../../utils/propertyRequestValidation";
import { useErrorHandler } from "./PropertyRequestErrorBoundary";
import {
  HomeIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

/**
 * Property Request Form Component
 * Captures user requirements for property requests
 */
const PropertyRequestForm = ({ onSuccess, onCancel }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const handleError = useErrorHandler();

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      property_type: "",
      sale_type: "",
      location_preference: "",
      min_budget: "",
      max_budget: "",
      min_bedrooms: "",
      max_bedrooms: "",
      min_bathrooms: "",
      max_bathrooms: "",
      min_square_feet: "",
      max_square_feet: "",
      specific_features: [],
      additional_requirements: "",
    },
  });

  const watchedValues = watch();

  const propertyTypeOptions = propertyRequestService.getPropertyTypeOptions();
  const saleTypeOptions = propertyRequestService.getSaleTypeOptions();

  const featureOptions = [
    "Swimming Pool",
    "Garden/Yard",
    "Garage",
    "Air Conditioning",
    "Gym/Fitness Center",
    "Security System",
    "Balcony/Terrace",
    "Fireplace",
    "Walk-in Closet",
    "Modern Kitchen",
    "Hardwood Floors",
    "Pet-Friendly",
  ];

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Client-side validation
      const validation = validatePropertyRequestForm(data);
      if (!validation.isValid) {
        const firstError = Object.values(validation.errors)[0];
        setSubmitError(firstError);
        setIsSubmitting(false);
        return;
      }

      // Convert specific_features from object to array
      const selectedFeatures = Object.entries(data.specific_features || {})
        .filter(([_, selected]) => selected)
        .map(([feature, _]) => feature);

      const requestData = {
        ...validation.sanitizedData,
        specific_features: selectedFeatures,
      };

      const { data: result, error } =
        await propertyRequestService.createPropertyRequest(requestData);

      if (error) {
        // Handle different types of errors
        if (error.message?.includes("rate limit")) {
          setSubmitError(getErrorMessage("RATE_LIMIT_ERROR"));
        } else if (
          error.message?.includes("network") ||
          error.message?.includes("fetch")
        ) {
          setSubmitError(getErrorMessage("NETWORK_ERROR"));
        } else if (error.message?.includes("validation")) {
          setSubmitError(getErrorMessage("VALIDATION_ERROR"));
        } else {
          setSubmitError(error.message || getErrorMessage("SERVER_ERROR"));
        }
        return;
      }

      setSubmitSuccess(true);
      reset();

      // Call success callback after a short delay to show success message
      setTimeout(() => {
        onSuccess?.(result);
      }, 2000);
    } catch (error) {
      console.error("Error submitting property request:", error);

      // Handle unexpected errors
      if (error.name === "TypeError" && error.message.includes("fetch")) {
        setSubmitError(getErrorMessage("NETWORK_ERROR"));
      } else if (error.name === "AbortError") {
        setSubmitError(getErrorMessage("TIMEOUT_ERROR"));
      } else {
        setSubmitError(getErrorMessage("UNKNOWN_ERROR"));
        // Report to error boundary for critical errors
        handleError(error);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-8"
      >
        <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-xl font-heading font-bold text-brown-dark dark:text-beige-light mb-2">
          Request Submitted Successfully!
        </h3>
        <p className="text-brown dark:text-beige-medium mb-4">
          Thank you for your property request. Our team will review your
          requirements and get back to you within 24 hours.
        </p>
        <p className="text-sm text-brown dark:text-beige-medium">
          You will receive a confirmation email shortly.
        </p>
      </motion.div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Contact Information Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <UserIcon className="h-5 w-5 text-taupe" />
          <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
            Contact Information
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Full Name *
            </label>
            <input
              type="text"
              {...register("name", {
                required: "Name is required",
                minLength: {
                  value: 2,
                  message: "Name must be at least 2 characters",
                },
              })}
              className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
                errors.name
                  ? "border-red-500"
                  : "border-brown/20 dark:border-beige-medium/20"
              }`}
              placeholder="Enter your full name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Email Address *
            </label>
            <input
              type="email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
              className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
                errors.email
                  ? "border-red-500"
                  : "border-brown/20 dark:border-beige-medium/20"
              }`}
              placeholder="Enter your email address"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-500">
                {errors.email.message}
              </p>
            )}
          </div>
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
            Phone Number (Optional)
          </label>
          <input
            type="tel"
            {...register("phone", {
              pattern: {
                value: /^[\+]?[\d\s\-\(\)\.]{7,20}$/,
                message: "Please enter a valid phone number",
              },
            })}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
              errors.phone
                ? "border-red-500"
                : "border-brown/20 dark:border-beige-medium/20"
            }`}
            placeholder="Enter your phone number"
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-500">{errors.phone.message}</p>
          )}
        </div>
      </div>

      {/* Property Requirements Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <HomeIcon className="h-5 w-5 text-taupe" />
          <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
            Property Requirements
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Property Type */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Property Type *
            </label>
            <select
              {...register("property_type", {
                required: "Property type is required",
              })}
              className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
                errors.property_type
                  ? "border-red-500"
                  : "border-brown/20 dark:border-beige-medium/20"
              }`}
            >
              <option value="">Select property type</option>
              {propertyTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.property_type && (
              <p className="mt-1 text-sm text-red-500">
                {errors.property_type.message}
              </p>
            )}
          </div>

          {/* Sale Type */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Looking to *
            </label>
            <select
              {...register("sale_type", { required: "Sale type is required" })}
              className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
                errors.sale_type
                  ? "border-red-500"
                  : "border-brown/20 dark:border-beige-medium/20"
              }`}
            >
              <option value="">Select option</option>
              {saleTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.sale_type && (
              <p className="mt-1 text-sm text-red-500">
                {errors.sale_type.message}
              </p>
            )}
          </div>
        </div>

        {/* Location Preference */}
        <div>
          <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
            Preferred Location *
          </label>
          <input
            type="text"
            {...register("location_preference", {
              required: "Location preference is required",
              minLength: {
                value: 2,
                message: "Location must be at least 2 characters",
              },
            })}
            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
              errors.location_preference
                ? "border-red-500"
                : "border-brown/20 dark:border-beige-medium/20"
            }`}
            placeholder="e.g., Downtown, Suburbs, Near schools"
          />
          {errors.location_preference && (
            <p className="mt-1 text-sm text-red-500">
              {errors.location_preference.message}
            </p>
          )}
        </div>
      </div>

      {/* Budget Range Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <CurrencyDollarIcon className="h-5 w-5 text-taupe" />
          <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
            Budget Range
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Min Budget */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Minimum Budget
            </label>
            <input
              type="number"
              {...register("min_budget", {
                min: { value: 0, message: "Budget must be positive" },
                validate: (value) => {
                  if (
                    value &&
                    watchedValues.max_budget &&
                    parseFloat(value) > parseFloat(watchedValues.max_budget)
                  ) {
                    return "Minimum budget cannot be greater than maximum budget";
                  }
                  return true;
                },
              })}
              className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
                errors.min_budget
                  ? "border-red-500"
                  : "border-brown/20 dark:border-beige-medium/20"
              }`}
              placeholder="0"
            />
            {errors.min_budget && (
              <p className="mt-1 text-sm text-red-500">
                {errors.min_budget.message}
              </p>
            )}
          </div>

          {/* Max Budget */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Maximum Budget
            </label>
            <input
              type="number"
              {...register("max_budget", {
                min: { value: 0, message: "Budget must be positive" },
                validate: (value) => {
                  if (
                    value &&
                    watchedValues.min_budget &&
                    parseFloat(value) < parseFloat(watchedValues.min_budget)
                  ) {
                    return "Maximum budget cannot be less than minimum budget";
                  }
                  return true;
                },
              })}
              className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors ${
                errors.max_budget
                  ? "border-red-500"
                  : "border-brown/20 dark:border-beige-medium/20"
              }`}
              placeholder="No limit"
            />
            {errors.max_budget && (
              <p className="mt-1 text-sm text-red-500">
                {errors.max_budget.message}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Property Size & Features Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <HomeIcon className="h-5 w-5 text-taupe" />
          <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
            Size Requirements
          </h3>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* Bedrooms */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Min Bedrooms
            </label>
            <input
              type="number"
              {...register("min_bedrooms", {
                min: { value: 0, message: "Must be 0 or more" },
              })}
              className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              placeholder="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Max Bedrooms
            </label>
            <input
              type="number"
              {...register("max_bedrooms", {
                min: { value: 0, message: "Must be 0 or more" },
              })}
              className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              placeholder="Any"
            />
          </div>

          {/* Bathrooms */}
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Min Bathrooms
            </label>
            <input
              type="number"
              {...register("min_bathrooms", {
                min: { value: 0, message: "Must be 0 or more" },
              })}
              className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              placeholder="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Max Bathrooms
            </label>
            <input
              type="number"
              {...register("max_bathrooms", {
                min: { value: 0, message: "Must be 0 or more" },
              })}
              className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              placeholder="Any"
            />
          </div>
        </div>

        {/* Square Feet */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Minimum Square Feet
            </label>
            <input
              type="number"
              {...register("min_square_feet", {
                min: { value: 0, message: "Must be positive" },
              })}
              className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              placeholder="e.g., 1000"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
              Maximum Square Feet
            </label>
            <input
              type="number"
              {...register("max_square_feet", {
                min: { value: 0, message: "Must be positive" },
              })}
              className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors"
              placeholder="No limit"
            />
          </div>
        </div>
      </div>

      {/* Desired Features Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <ChatBubbleLeftRightIcon className="h-5 w-5 text-taupe" />
          <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
            Desired Features
          </h3>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {featureOptions.map((feature) => (
            <label
              key={feature}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <input
                type="checkbox"
                {...register(`specific_features.${feature}`)}
                className="rounded border-brown/20 dark:border-beige-medium/20 text-taupe focus:ring-taupe focus:ring-offset-0"
              />
              <span className="text-sm text-brown-dark dark:text-beige-light">
                {feature}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Additional Requirements */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <ChatBubbleLeftRightIcon className="h-5 w-5 text-taupe" />
          <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
            Additional Requirements
          </h3>
        </div>

        <div>
          <label className="block text-sm font-medium text-brown-dark dark:text-beige-light mb-1">
            Tell us more about what you're looking for
          </label>
          <textarea
            {...register("additional_requirements")}
            rows={4}
            className="w-full px-3 py-2 border border-brown/20 dark:border-beige-medium/20 rounded-lg bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light placeholder-brown/50 dark:placeholder-beige-medium/50 focus:outline-none focus:ring-2 focus:ring-taupe focus:border-taupe transition-colors resize-vertical"
            placeholder="Any specific requirements, preferences, or questions you'd like to share..."
          />
        </div>
      </div>

      {/* Error Message */}
      {submitError && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <ExclamationTriangleIcon className="h-5 w-5 text-red-500 flex-shrink-0" />
          <p className="text-sm text-red-700 dark:text-red-400">
            {submitError}
          </p>
        </motion.div>
      )}

      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-6 py-2 border border-brown/20 dark:border-beige-medium/20 text-brown dark:text-beige-medium rounded-lg hover:bg-brown/5 dark:hover:bg-beige-medium/5 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="flex-1 px-6 py-2 bg-taupe text-white rounded-lg hover:bg-taupe/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
        >
          {isSubmitting ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
              Submitting...
            </>
          ) : (
            "Submit Request"
          )}
        </button>
      </div>
    </form>
  );
};

export default PropertyRequestForm;
