/**
 * Security Utilities for Property Request System
 * Provides input sanitization, CSRF protection, and other security measures
 */

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param {string} input - Input string to sanitize
 * @returns {string} Sanitized string
 */
export const sanitizeHtml = (input) => {
  if (typeof input !== "string") return "";

  // Create a temporary div element to leverage browser's HTML parsing
  const temp = document.createElement("div");
  temp.textContent = input;
  return temp.innerHTML;
};

/**
 * Sanitize user input by removing potentially dangerous characters
 * @param {string} input - Input string to sanitize
 * @returns {string} Sanitized string
 */
export const sanitizeInput = (input) => {
  if (typeof input !== "string") return "";

  return (
    input
      .trim()
      // Remove null bytes
      .replace(/\0/g, "")
      // Remove control characters except newlines and tabs
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, "")
      // Limit length to prevent DoS
      .substring(0, 10000)
  );
};

/**
 * Validate and sanitize email addresses
 * @param {string} email - Email address to validate and sanitize
 * @returns {Object} Result with isValid boolean and sanitized email
 */
export const sanitizeEmail = (email) => {
  if (typeof email !== "string") {
    return { isValid: false, sanitized: "" };
  }

  const sanitized = email.trim().toLowerCase();
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  return {
    isValid: emailRegex.test(sanitized) && sanitized.length <= 254,
    sanitized,
  };
};

/**
 * Validate and sanitize phone numbers
 * @param {string} phone - Phone number to validate and sanitize
 * @returns {Object} Result with isValid boolean and sanitized phone
 */
export const sanitizePhone = (phone) => {
  if (!phone || typeof phone !== "string") {
    return { isValid: true, sanitized: null }; // Phone is optional
  }

  // Remove all non-digit characters except +, -, (, ), space, and .
  const sanitized = phone.replace(/[^\d\+\-\(\)\s\.]/g, "").trim();
  const phoneRegex = /^[\+]?[\d\s\-\(\)\.]{7,20}$/;

  return {
    isValid: phoneRegex.test(sanitized),
    sanitized: sanitized || null,
  };
};

/**
 * Rate limiter for client-side requests
 */
class ClientRateLimiter {
  constructor() {
    this.requests = new Map();
  }

  /**
   * Check if request is allowed based on rate limiting
   * @param {string} key - Unique key for the request (e.g., user ID, IP)
   * @param {number} maxRequests - Maximum requests allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {boolean} True if request is allowed
   */
  isAllowed(key, maxRequests = 5, windowMs = 15 * 60 * 1000) {
    const now = Date.now();
    const userRequests = this.requests.get(key) || [];

    // Remove old requests outside the time window
    const validRequests = userRequests.filter(
      (timestamp) => now - timestamp < windowMs
    );

    if (validRequests.length >= maxRequests) {
      return false;
    }

    // Add current request
    validRequests.push(now);
    this.requests.set(key, validRequests);

    return true;
  }

  /**
   * Get remaining requests for a key
   * @param {string} key - Unique key for the request
   * @param {number} maxRequests - Maximum requests allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {number} Number of remaining requests
   */
  getRemainingRequests(key, maxRequests = 5, windowMs = 15 * 60 * 1000) {
    const now = Date.now();
    const userRequests = this.requests.get(key) || [];
    const validRequests = userRequests.filter(
      (timestamp) => now - timestamp < windowMs
    );

    return Math.max(0, maxRequests - validRequests.length);
  }

  /**
   * Clear all stored requests (useful for testing)
   */
  clear() {
    this.requests.clear();
  }
}

// Global rate limiter instance
export const rateLimiter = new ClientRateLimiter();

/**
 * Generate a simple CSRF token (for client-side use)
 * Note: This is a basic implementation. For production, use server-generated tokens
 * @returns {string} CSRF token
 */
export const generateCSRFToken = () => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
    ""
  );
};

/**
 * Store CSRF token in session storage
 * @param {string} token - CSRF token to store
 */
export const storeCSRFToken = (token) => {
  try {
    sessionStorage.setItem("csrf_token", token);
  } catch (error) {
    console.warn("Failed to store CSRF token:", error);
  }
};

/**
 * Get CSRF token from session storage
 * @returns {string|null} CSRF token or null if not found
 */
export const getCSRFToken = () => {
  try {
    return sessionStorage.getItem("csrf_token");
  } catch (error) {
    console.warn("Failed to get CSRF token:", error);
    return null;
  }
};

/**
 * Validate content length to prevent DoS attacks
 * @param {string} content - Content to validate
 * @param {number} maxLength - Maximum allowed length
 * @returns {boolean} True if content length is valid
 */
export const validateContentLength = (content, maxLength = 2000) => {
  if (typeof content !== "string") return false;
  return content.length <= maxLength;
};

/**
 * Check for suspicious patterns in user input
 * @param {string} input - Input to check
 * @returns {boolean} True if input appears suspicious
 */
export const detectSuspiciousInput = (input) => {
  if (typeof input !== "string") return false;

  const suspiciousPatterns = [
    // SQL injection patterns
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    // Script injection patterns
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    // Common XSS patterns
    /javascript:/i,
    /on\w+\s*=/i,
    // Path traversal
    /\.\.\//,
    // Null bytes
    /\0/,
    // Excessive repetition (potential DoS)
    /(.)\1{100,}/,
  ];

  return suspiciousPatterns.some((pattern) => pattern.test(input));
};

/**
 * Sanitize property request data
 * @param {Object} data - Property request data to sanitize
 * @returns {Object} Sanitized data
 */
export const sanitizePropertyRequestData = (data) => {
  const sanitized = {};

  // Sanitize string fields
  const stringFields = [
    "name",
    "location_preference",
    "additional_requirements",
  ];
  stringFields.forEach((field) => {
    if (data[field]) {
      sanitized[field] = sanitizeInput(data[field]);

      // Check for suspicious content
      if (detectSuspiciousInput(sanitized[field])) {
        throw new Error(`Suspicious content detected in ${field}`);
      }
    }
  });

  // Sanitize email
  if (data.email) {
    const emailResult = sanitizeEmail(data.email);
    if (!emailResult.isValid) {
      throw new Error("Invalid email address");
    }
    sanitized.email = emailResult.sanitized;
  }

  // Sanitize phone
  if (data.phone) {
    const phoneResult = sanitizePhone(data.phone);
    if (!phoneResult.isValid) {
      throw new Error("Invalid phone number");
    }
    sanitized.phone = phoneResult.sanitized;
  }

  // Validate and sanitize numeric fields
  const numericFields = [
    "min_budget",
    "max_budget",
    "min_bedrooms",
    "max_bedrooms",
    "min_bathrooms",
    "max_bathrooms",
    "min_square_feet",
    "max_square_feet",
  ];
  numericFields.forEach((field) => {
    if (
      data[field] !== undefined &&
      data[field] !== null &&
      data[field] !== ""
    ) {
      const num = parseFloat(data[field]);
      if (isNaN(num) || num < 0 || num > 1000000000) {
        throw new Error(`Invalid value for ${field}`);
      }
      sanitized[field] = num;
    }
  });

  // Validate enum fields
  const validPropertyTypes = [
    "house",
    "apartment",
    "condo",
    "land",
    "commercial",
    "townhouse",
    "other",
  ];
  const validSaleTypes = ["rent", "buy"];

  if (data.property_type && !validPropertyTypes.includes(data.property_type)) {
    throw new Error("Invalid property type");
  }

  if (data.sale_type && !validSaleTypes.includes(data.sale_type)) {
    throw new Error("Invalid sale type");
  }

  sanitized.property_type = data.property_type;
  sanitized.sale_type = data.sale_type;

  // Sanitize array fields
  if (data.specific_features && Array.isArray(data.specific_features)) {
    sanitized.specific_features = data.specific_features
      .filter((feature) => typeof feature === "string")
      .map((feature) => sanitizeInput(feature))
      .filter((feature) => feature.length > 0 && feature.length <= 100)
      .slice(0, 20); // Limit to 20 features
  }

  return sanitized;
};

/**
 * Check if we're in development environment
 * @returns {boolean} True if in development
 */
const isDevelopmentEnvironment = () => {
  try {
    return (
      (typeof process !== "undefined" &&
        process.env?.NODE_ENV === "development") ||
      (typeof import.meta !== "undefined" && import.meta.env?.DEV) ||
      (typeof window !== "undefined" &&
        (window.location.hostname === "localhost" ||
          window.location.hostname === "127.0.0.1" ||
          window.location.port === "5173" ||
          window.location.port === "5174"))
    );
  } catch {
    return false;
  }
};

/**
 * Get appropriate Content Security Policy based on environment
 * @returns {string} CSP directive string
 */
const getContentSecurityPolicy = () => {
  const isDevelopment = isDevelopmentEnvironment();

  if (isDevelopment) {
    // More permissive CSP for development
    return `
      default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: http: https:;
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:;
      style-src 'self' 'unsafe-inline' https: http:;
      img-src 'self' data: blob: https: http:;
      font-src 'self' data: https: http:;
      connect-src 'self' ws: wss: https: http:;
      frame-src 'self' https: http:;
      media-src 'self' data: blob: https: http:;
    `
      .replace(/\s+/g, " ")
      .trim();
  }

  // Production CSP - more restrictive but allows necessary external resources
  return `
    default-src 'self';
    script-src 'self' 'unsafe-inline' https://cdn.jotfor.ms https://js.jotfor.ms;
    style-src 'self' 'unsafe-inline' https://cdn.jotfor.ms https://fonts.googleapis.com;
    img-src 'self' data: blob: https: http:;
    font-src 'self' data: https://fonts.gstatic.com https://cdn.jotfor.ms;
    connect-src 'self' https://*.supabase.co https://api.jotfor.ms https://submit.jotfor.ms;
    frame-src 'self' https://cdn.jotfor.ms https://form.jotfor.ms;
    media-src 'self' data: blob:;
    object-src 'none';
    base-uri 'self';
    form-action 'self' https://submit.jotfor.ms;
  `
    .replace(/\s+/g, " ")
    .trim();
};

/**
 * Set up CSP violation reporting for debugging
 */
const setupCSPReporting = () => {
  if (typeof document !== "undefined") {
    document.addEventListener("securitypolicyviolation", (event) => {
      const isDevelopment = isDevelopmentEnvironment();

      if (isDevelopment) {
        console.group("🚨 Content Security Policy Violation");
        console.warn("Blocked URI:", event.blockedURI);
        console.warn("Violated Directive:", event.violatedDirective);
        console.warn("Original Policy:", event.originalPolicy);
        console.warn("Source File:", event.sourceFile);
        console.warn("Line Number:", event.lineNumber);
        console.groupEnd();

        // Provide helpful suggestions
        if (event.blockedURI.includes("jotfor.ms")) {
          console.info(
            "💡 JotForm chatbot blocked. This is expected in development mode with strict CSP."
          );
        } else if (event.blockedURI.includes("supabase.co")) {
          console.info(
            "💡 Supabase connection blocked. Check if the domain is whitelisted in CSP."
          );
        }
      }
    });
  }
};

/**
 * Initialize security measures for the application
 */
export const initializeSecurity = () => {
  // Generate and store CSRF token if not exists
  if (!getCSRFToken()) {
    const token = generateCSRFToken();
    storeCSRFToken(token);
  }

  // Set up Content Security Policy headers (if supported)
  if (typeof document !== "undefined") {
    // Remove any existing CSP meta tags to avoid conflicts
    const existingCSP = document.querySelector(
      'meta[http-equiv="Content-Security-Policy"]'
    );
    if (existingCSP) {
      existingCSP.remove();
    }

    const meta = document.createElement("meta");
    meta.httpEquiv = "Content-Security-Policy";
    meta.content = getContentSecurityPolicy();
    document.head.appendChild(meta);

    // Set up CSP violation reporting for debugging
    setupCSPReporting();

    // Log CSP in development for debugging
    if (isDevelopmentEnvironment()) {
      console.log("🔒 Content Security Policy applied:", meta.content);
      console.info("🔍 CSP violation reporting enabled for debugging");
    }
  }
};
