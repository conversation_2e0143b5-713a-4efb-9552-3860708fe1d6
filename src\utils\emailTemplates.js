/**
 * Email Templates for Property Request System
 * Contains HTML templates for admin notifications and user confirmations
 */

/**
 * Generate user confirmation email template
 * @param {Object} request - Property request data
 * @param {string} request.name - User's name
 * @param {string} request.property_type - Type of property requested
 * @param {string} request.sale_type - Sale type (buy/rent)
 * @param {string} request.location_preference - Preferred location
 * @returns {string} HTML email template
 */
export function generateUserConfirmationTemplate(request) {
  const formatPropertyType = (type) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const formatSaleType = (type) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Request Confirmation - UrbanEdge Real Estate</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background-color: #ffffff; 
            border-radius: 8px; 
            overflow: hidden; 
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); 
        }
        .header { 
            background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 { 
            margin: 0; 
            font-size: 24px; 
            font-weight: 600; 
        }
        .header p { 
            margin: 10px 0 0 0; 
            opacity: 0.9; 
        }
        .content { 
            padding: 30px 20px; 
        }
        .section { 
            margin-bottom: 25px; 
        }
        .highlight-box { 
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); 
            border-left: 4px solid #8B4513; 
            padding: 20px; 
            border-radius: 6px; 
            margin: 20px 0; 
        }
        .info-grid { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 15px; 
            margin: 15px 0; 
        }
        .info-item { 
            background-color: #f9f9f9; 
            padding: 12px; 
            border-radius: 6px; 
        }
        .info-label { 
            font-weight: 600; 
            color: #666; 
            font-size: 12px; 
            text-transform: uppercase; 
            margin-bottom: 4px; 
        }
        .info-value { 
            color: #333; 
            font-size: 14px; 
        }
        .cta { 
            background-color: #8B4513; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 6px; 
            display: inline-block; 
            margin: 20px 0; 
            font-weight: 600; 
        }
        .footer { 
            background-color: #f9f9f9; 
            padding: 20px; 
            text-align: center; 
            color: #666; 
            font-size: 12px; 
        }
        .contact-info { 
            background-color: #fff8f0; 
            border: 1px solid #8B4513; 
            border-radius: 6px; 
            padding: 15px; 
            margin: 20px 0; 
        }
        @media (max-width: 600px) {
            .info-grid { grid-template-columns: 1fr; }
            .container { margin: 0; border-radius: 0; }
            .content { padding: 20px 15px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Request Received!</h1>
            <p>Thank you for choosing UrbanEdge Real Estate</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2 style="color: #8B4513; margin-bottom: 15px;">Hello ${request.name},</h2>
                <p>We've successfully received your property request and our team is already working to find the perfect match for you.</p>
            </div>

            <div class="highlight-box">
                <h3 style="margin-top: 0; color: #8B4513;">Your Request Summary</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Property Type</div>
                        <div class="info-value">${formatPropertyType(request.property_type)}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Looking to</div>
                        <div class="info-value">${formatSaleType(request.sale_type)}</div>
                    </div>
                    <div class="info-item" style="grid-column: 1 / -1;">
                        <div class="info-label">Preferred Location</div>
                        <div class="info-value">${request.location_preference}</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3 style="color: #8B4513;">What Happens Next?</h3>
                <ul style="padding-left: 20px;">
                    <li><strong>Within 24 hours:</strong> Our team will review your requirements and search our database</li>
                    <li><strong>Within 48 hours:</strong> We'll contact you with initial property matches</li>
                    <li><strong>Ongoing:</strong> We'll continue to monitor new listings that match your criteria</li>
                </ul>
            </div>

            <div class="contact-info">
                <h4 style="margin-top: 0; color: #8B4513;">Need to Update Your Request?</h4>
                <p style="margin-bottom: 10px;">If you need to modify your requirements or have any questions, don't hesitate to reach out:</p>
                <p style="margin: 5px 0;"><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #8B4513;"><EMAIL></a></p>
                <p style="margin: 5px 0;"><strong>Phone:</strong> <a href="tel:+1234567890" style="color: #8B4513;">(*************</a></p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="${process.env.REACT_APP_SITE_URL || 'https://urbanedge.com'}/properties" class="cta">
                    Browse Current Listings
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>UrbanEdge Real Estate</strong></p>
            <p>Your trusted partner in finding the perfect property</p>
            <p style="margin-top: 15px;">
                <a href="${process.env.REACT_APP_SITE_URL || 'https://urbanedge.com'}" style="color: #8B4513;">Visit our website</a> | 
                <a href="${process.env.REACT_APP_SITE_URL || 'https://urbanedge.com'}/contact" style="color: #8B4513;">Contact us</a>
            </p>
        </div>
    </div>
</body>
</html>
  `;
}

/**
 * Generate admin notification email template
 * @param {Object} request - Property request data
 * @returns {string} HTML email template
 */
export function generateAdminNotificationTemplate(request) {
  const formatBudget = (amount) => {
    if (!amount) return 'Not specified';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatBudgetRange = (min, max) => {
    if (!min && !max) return 'Not specified';
    if (!min) return `Up to ${formatBudget(max)}`;
    if (!max) return `From ${formatBudget(min)}`;
    return `${formatBudget(min)} - ${formatBudget(max)}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPropertyType = (type) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const formatSaleType = (type) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Property Request - UrbanEdge Real Estate</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content { padding: 30px 20px; }
        .section { margin-bottom: 25px; }
        .section h2 { color: #8B4513; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f0f0f0; padding-bottom: 8px; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
        .info-item { background-color: #f9f9f9; padding: 12px; border-radius: 6px; }
        .info-label { font-weight: 600; color: #666; font-size: 12px; text-transform: uppercase; margin-bottom: 4px; }
        .info-value { color: #333; font-size: 14px; }
        .features { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 10px; }
        .feature-tag { background-color: #8B4513; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; }
        .cta { background-color: #8B4513; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-top: 20px; font-weight: 600; }
        .footer { background-color: #f9f9f9; padding: 20px; text-align: center; color: #666; font-size: 12px; }
        .urgent { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 15px 0; }
        @media (max-width: 600px) {
            .info-grid { grid-template-columns: 1fr; }
            .container { margin: 0; border-radius: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 New Property Request</h1>
            <p>A potential client has submitted a property request</p>
        </div>
        
        <div class="content">
            <div class="urgent">
                <strong>⚡ Action Required:</strong> A new property request needs your attention. Please review and respond within 24 hours.
            </div>

            <div class="section">
                <h2>👤 Contact Information</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Name</div>
                        <div class="info-value">${request.name}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email</div>
                        <div class="info-value"><a href="mailto:${request.email}" style="color: #8B4513;">${request.email}</a></div>
                    </div>
                    ${request.phone ? `
                    <div class="info-item">
                        <div class="info-label">Phone</div>
                        <div class="info-value"><a href="tel:${request.phone}" style="color: #8B4513;">${request.phone}</a></div>
                    </div>
                    ` : ''}
                </div>
            </div>

            <div class="section">
                <h2>🏡 Property Requirements</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Property Type</div>
                        <div class="info-value">${formatPropertyType(request.property_type)}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Looking to</div>
                        <div class="info-value">${formatSaleType(request.sale_type)}</div>
                    </div>
                    <div class="info-item" style="grid-column: 1 / -1;">
                        <div class="info-label">Location Preference</div>
                        <div class="info-value">${request.location_preference}</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>💰 Budget & Size</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Budget Range</div>
                        <div class="info-value">${formatBudgetRange(request.min_budget, request.max_budget)}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bedrooms</div>
                        <div class="info-value">${request.min_bedrooms || 0} - ${request.max_bedrooms || '∞'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bathrooms</div>
                        <div class="info-value">${request.min_bathrooms || 0} - ${request.max_bathrooms || '∞'}</div>
                    </div>
                    ${(request.min_square_feet || request.max_square_feet) ? `
                    <div class="info-item">
                        <div class="info-label">Square Feet</div>
                        <div class="info-value">${request.min_square_feet || 0} - ${request.max_square_feet || '∞'}</div>
                    </div>
                    ` : ''}
                </div>
            </div>

            ${(request.specific_features && request.specific_features.length > 0) ? `
            <div class="section">
                <h2>✨ Desired Features</h2>
                <div class="features">
                    ${request.specific_features.map(feature => `<span class="feature-tag">${feature}</span>`).join('')}
                </div>
            </div>
            ` : ''}

            ${request.additional_requirements ? `
            <div class="section">
                <h2>📝 Additional Requirements</h2>
                <div style="background-color: #f9f9f9; padding: 15px; border-radius: 6px; white-space: pre-wrap;">${request.additional_requirements}</div>
            </div>
            ` : ''}

            <div class="section">
                <h2>📅 Request Details</h2>
                <div class="info-item">
                    <div class="info-label">Submitted</div>
                    <div class="info-value">${formatDate(request.created_at)}</div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="${process.env.REACT_APP_ADMIN_DASHBOARD_URL || 'https://urbanedge.com/admin/dashboard'}?tab=requests" class="cta">
                    View in Admin Dashboard
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>UrbanEdge Real Estate Admin System</strong></p>
            <p>This is an automated notification. Please respond to the client directly.</p>
        </div>
    </div>
</body>
</html>
  `;
}

/**
 * Generate plain text version of user confirmation email
 * @param {Object} request - Property request data
 * @returns {string} Plain text email content
 */
export function generateUserConfirmationText(request) {
  return `
Property Request Confirmation - UrbanEdge Real Estate

Hello ${request.name},

We've successfully received your property request and our team is already working to find the perfect match for you.

Your Request Summary:
- Property Type: ${request.property_type.charAt(0).toUpperCase() + request.property_type.slice(1)}
- Looking to: ${request.sale_type.charAt(0).toUpperCase() + request.sale_type.slice(1)}
- Preferred Location: ${request.location_preference}

What Happens Next?
- Within 24 hours: Our team will review your requirements and search our database
- Within 48 hours: We'll contact you with initial property matches
- Ongoing: We'll continue to monitor new listings that match your criteria

Need to Update Your Request?
If you need to modify your requirements or have any questions, don't hesitate to reach out:
Email: <EMAIL>
Phone: (*************

Browse Current Listings: ${process.env.REACT_APP_SITE_URL || 'https://urbanedge.com'}/properties

UrbanEdge Real Estate
Your trusted partner in finding the perfect property
  `;
}
