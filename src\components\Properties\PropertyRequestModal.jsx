import { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { XMarkIcon } from "@heroicons/react/24/outline";
import PropertyRequestForm from "./PropertyRequestForm";
import PropertyRequestErrorBoundary, {
  PropertyRequestFormErrorFallback,
} from "./PropertyRequestErrorBoundary";

/**
 * Property Request Modal Component
 * A reusable modal that wraps the property request form with proper animations and accessibility
 */
const PropertyRequestModal = ({ isOpen, onClose, onSuccess }) => {
  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      // Restore body scroll when modal is closed
      document.body.style.overflow = "auto";
    };
  }, [isOpen, onClose]);

  const handleSuccess = (result) => {
    // Close modal after success
    setTimeout(() => {
      onClose();
      onSuccess?.(result);
    }, 2500);
  };

  const handleBackdropClick = (event) => {
    // Only close if clicking the backdrop, not the modal content
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-brown-dark/80 backdrop-blur-sm"
          onClick={handleBackdropClick}
          role="dialog"
          aria-modal="true"
          aria-labelledby="property-request-modal-title"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="bg-white dark:bg-brown-dark rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-brown/10 dark:border-beige-medium/10">
              <div>
                <h2
                  id="property-request-modal-title"
                  className="text-2xl font-heading font-bold text-brown-dark dark:text-beige-light"
                >
                  Can't Find What You're Looking For?
                </h2>
                <p className="text-brown dark:text-beige-medium mt-1">
                  Tell us what you need and we'll help you find the perfect
                  property
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-brown dark:text-beige-medium hover:text-brown-dark dark:hover:text-beige-light hover:bg-brown/5 dark:hover:bg-beige-medium/5 rounded-lg transition-colors"
                aria-label="Close modal"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <PropertyRequestErrorBoundary
                fallback={PropertyRequestFormErrorFallback}
              >
                <PropertyRequestForm
                  onSuccess={handleSuccess}
                  onCancel={onClose}
                />
              </PropertyRequestErrorBoundary>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PropertyRequestModal;
