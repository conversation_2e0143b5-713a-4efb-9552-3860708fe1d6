# Security and Content Security Policy (CSP) Guide

## Overview

This document explains the security measures implemented in the UrbanEdge real estate platform, particularly focusing on Content Security Policy (CSP) and how to handle external integrations like the JotForm chatbot.

## Content Security Policy (CSP)

### What is CSP?

Content Security Policy is a security feature that helps prevent Cross-Site Scripting (XSS) attacks by controlling which resources (scripts, styles, images, etc.) can be loaded by your web application.

### Environment-Based CSP

Our implementation uses different CSP configurations based on the environment:

#### Development Environment
- **More permissive** to allow for development tools, hot reloading, and external CDNs
- Allows most external resources for easier development
- Includes detailed violation reporting for debugging

#### Production Environment
- **More restrictive** for better security
- Specifically allows only necessary external domains:
  - JotForm chatbot (`cdn.jotfor.ms`, `js.jotfor.ms`)
  - Supabase API (`*.supabase.co`)
  - Google Fonts (`fonts.googleapis.com`, `fonts.gstatic.com`)

### CSP Configuration

The CSP is configured in `src/utils/security.js`:

```javascript
// Development CSP - Permissive
default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: http: https:;
script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:;
// ... more permissive directives

// Production CSP - Restrictive but functional
default-src 'self';
script-src 'self' 'unsafe-inline' https://cdn.jotfor.ms https://js.jotfor.ms;
connect-src 'self' https://*.supabase.co https://api.jotfor.ms;
// ... specific allowed domains
```

## JotForm Chatbot Integration

### The Problem

The JotForm chatbot requires loading external scripts from `cdn.jotfor.ms`, which can be blocked by strict CSP policies.

### The Solution

1. **Environment Detection**: The system detects if it's running in development or production
2. **Conditional CSP**: Applies appropriate CSP based on environment
3. **Graceful Fallback**: The chatbot component handles CSP violations gracefully
4. **Debugging Support**: Provides helpful console messages for developers

### Chatbot Implementation

The chatbot is implemented in `src/components/UI/ChatbotWidget.jsx`:

```javascript
// Dynamic script loading with error handling
const script = document.createElement("script");
script.src = "https://cdn.jotfor.ms/agent/embedjs/YOUR_ID/embed.js?skipWelcome=1&maximizable=1";

script.onerror = (error) => {
  if (error.message && error.message.includes("Content Security Policy")) {
    console.warn("🔒 JotForm blocked by CSP. Expected in development.");
  }
};
```

## Supabase Integration

### CSP Requirements

Supabase requires the following CSP directives:

```
connect-src 'self' https://*.supabase.co;
```

This allows:
- API calls to your Supabase project
- Authentication requests
- Real-time subscriptions

### Common Issues

1. **Auth Token Refresh Failures**: Ensure `connect-src` includes your Supabase URL
2. **Real-time Subscriptions**: WebSocket connections need `ws:` and `wss:` protocols
3. **File Uploads**: May require additional `blob:` and `data:` sources

## Troubleshooting CSP Issues

### 1. Check Browser Console

CSP violations are logged in the browser console:

```
Refused to load the script 'https://example.com/script.js' because it violates the following Content Security Policy directive: "script-src 'self'"
```

### 2. CSP Violation Reporting

Our implementation includes automatic CSP violation reporting in development:

```javascript
document.addEventListener('securitypolicyviolation', (event) => {
  console.group('🚨 Content Security Policy Violation');
  console.warn('Blocked URI:', event.blockedURI);
  console.warn('Violated Directive:', event.violatedDirective);
  // ... more details
});
```

### 3. Common Solutions

#### Adding External Domains

To allow a new external service, update the CSP in `src/utils/security.js`:

```javascript
// For scripts
script-src 'self' 'unsafe-inline' https://your-new-domain.com;

// For API calls
connect-src 'self' https://*.supabase.co https://your-api-domain.com;

// For iframes
frame-src 'self' https://your-iframe-domain.com;
```

#### Development vs Production

Use environment detection to apply different policies:

```javascript
const isDevelopment = isDevelopmentEnvironment();

if (isDevelopment) {
  // More permissive for development
} else {
  // Strict for production
}
```

## Security Best Practices

### 1. Input Sanitization

All user inputs are sanitized using utilities in `src/utils/security.js`:

```javascript
import { sanitizePropertyRequestData } from '../utils/security';

const sanitizedData = sanitizePropertyRequestData(formData);
```

### 2. Rate Limiting

Client-side rate limiting prevents spam:

```javascript
import { rateLimiter } from '../utils/security';

if (!rateLimiter.isAllowed(userKey, 3, 15 * 60 * 1000)) {
  throw new Error('Rate limit exceeded');
}
```

### 3. CSRF Protection

CSRF tokens are automatically generated and stored:

```javascript
import { generateCSRFToken, storeCSRFToken } from '../utils/security';

const token = generateCSRFToken();
storeCSRFToken(token);
```

## Adding New External Services

When integrating new external services:

1. **Identify Required Domains**: Check what domains the service uses
2. **Update CSP**: Add necessary domains to the production CSP
3. **Test in Development**: Ensure it works in both environments
4. **Handle Errors**: Implement graceful fallbacks for CSP violations
5. **Document Changes**: Update this guide with new requirements

### Example: Adding Google Analytics

```javascript
// In getContentSecurityPolicy() production section:
script-src 'self' 'unsafe-inline' 
  https://cdn.jotfor.ms 
  https://www.googletagmanager.com 
  https://www.google-analytics.com;

connect-src 'self' 
  https://*.supabase.co 
  https://www.google-analytics.com;
```

## Environment Variables

The CSP system uses these environment detection methods:

1. `process.env.NODE_ENV === 'development'` (Node.js)
2. `import.meta.env.DEV` (Vite)
3. `window.location.hostname` (Browser)
4. `window.location.port` (Development servers)

## Testing CSP

### 1. Development Testing

- Check browser console for violations
- Verify external services load correctly
- Test form submissions and API calls

### 2. Production Testing

- Use browser dev tools to simulate production CSP
- Test with CSP header instead of meta tag
- Verify all external integrations work

### 3. CSP Testing Tools

- [CSP Evaluator](https://csp-evaluator.withgoogle.com/)
- Browser dev tools Security tab
- CSP violation reporting endpoints

## Maintenance

### Regular Updates

1. **Review External Services**: Periodically check if external services have changed domains
2. **Update CSP**: Adjust policies as needed for new integrations
3. **Security Audits**: Regularly review and tighten CSP policies
4. **Monitor Violations**: Set up production CSP violation reporting

### Documentation

Keep this document updated when:
- Adding new external services
- Changing CSP policies
- Discovering new security requirements
- Implementing new security features

## Support

For CSP-related issues:

1. Check browser console for violation details
2. Review this documentation
3. Test in both development and production environments
4. Consider security implications of any CSP changes
