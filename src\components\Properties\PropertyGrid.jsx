import { motion } from "framer-motion";
import {
  PlusCircleIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import PropertyCard from "../UI/PropertyCard";

const PropertyGrid = ({ properties, loading, onRequestProperty }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div
            key={index}
            className="bg-beige-medium dark:bg-brown animate-pulse rounded-lg h-96"
          ></div>
        ))}
      </div>
    );
  }

  if (properties.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mb-8">
          <MagnifyingGlassIcon className="h-16 w-16 text-brown/30 dark:text-beige-medium/30 mx-auto mb-4" />
          <h3 className="text-xl font-heading font-semibold text-brown-dark dark:text-beige-light mb-2">
            No properties found
          </h3>
          <p className="text-brown dark:text-beige-medium mb-6">
            Try adjusting your filters to see more results, or let us know what
            you're looking for.
          </p>
        </div>

        {/* Property Request CTA */}
        <div className="bg-beige-light dark:bg-brown-dark border border-brown/10 dark:border-beige-medium/10 rounded-xl p-6 max-w-md mx-auto">
          <h4 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light mb-2">
            Can't find what you're looking for?
          </h4>
          <p className="text-brown dark:text-beige-medium text-sm mb-4">
            Tell us your requirements and we'll help you find the perfect
            property or notify you when something matching becomes available.
          </p>
          <button
            onClick={onRequestProperty}
            className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-taupe text-white rounded-lg hover:bg-taupe/90 transition-colors font-medium"
          >
            <PlusCircleIcon className="h-5 w-5" />
            Request a Property
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {properties.map((property, index) => (
          <motion.div
            key={property.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
          >
            <PropertyCard property={property} />
          </motion.div>
        ))}
      </div>

      {/* Property Request CTA - Always show when there are properties */}
      {properties.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: properties.length * 0.1 + 0.2 }}
          className="bg-gradient-to-r from-taupe/10 to-brown/10 dark:from-taupe/20 dark:to-brown/20 border border-taupe/20 dark:border-taupe/30 rounded-xl p-6 text-center"
        >
          <h4 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light mb-2">
            Don't see exactly what you need?
          </h4>
          <p className="text-brown dark:text-beige-medium text-sm mb-4 max-w-md mx-auto">
            We have access to exclusive listings and off-market properties. Let
            us know your specific requirements and we'll find the perfect match.
          </p>
          <button
            onClick={onRequestProperty}
            className="inline-flex items-center gap-2 px-6 py-3 bg-taupe text-white rounded-lg hover:bg-taupe/90 transition-colors font-medium"
          >
            <PlusCircleIcon className="h-5 w-5" />
            Request Custom Property Search
          </button>
        </motion.div>
      )}
    </div>
  );
};

export default PropertyGrid;
