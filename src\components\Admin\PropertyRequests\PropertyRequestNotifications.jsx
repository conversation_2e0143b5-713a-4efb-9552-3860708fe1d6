import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { propertyRequestService } from '../../../lib/propertyRequestService';
import {
  BellIcon,
  XMarkIcon,
  ClockIcon,
  UserIcon,
  HomeIcon,
  MapPinIcon
} from '@heroicons/react/24/outline';

/**
 * Property Request Notifications Component
 * Shows real-time notifications for new property requests
 */
const PropertyRequestNotifications = ({ onRequestClick }) => {
  const [notifications, setNotifications] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    // Subscribe to property request changes
    const subscription = propertyRequestService.subscribeToPropertyRequests((payload) => {
      if (payload.eventType === 'INSERT') {
        const newRequest = payload.new;
        
        // Add notification for new request
        const notification = {
          id: newRequest.id,
          type: 'new_request',
          title: 'New Property Request',
          message: `${newRequest.name} is looking for a ${newRequest.property_type} to ${newRequest.sale_type}`,
          data: newRequest,
          timestamp: new Date(),
          read: false
        };

        setNotifications(prev => [notification, ...prev.slice(0, 9)]); // Keep only 10 most recent
        setUnreadCount(prev => prev + 1);

        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
          new Notification('New Property Request', {
            body: notification.message,
            icon: '/favicon.ico',
            tag: `property-request-${newRequest.id}`
          });
        }
      }
    });

    // Request notification permission
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notif => ({ ...notif, read: true })));
    setUnreadCount(0);
  };

  const removeNotification = (notificationId) => {
    setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
    setUnreadCount(prev => {
      const notification = notifications.find(n => n.id === notificationId);
      return notification && !notification.read ? Math.max(0, prev - 1) : prev;
    });
  };

  const handleNotificationClick = (notification) => {
    markAsRead(notification.id);
    onRequestClick?.(notification.data);
    setIsOpen(false);
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-brown dark:text-beige-medium hover:text-brown-dark dark:hover:text-beige-light hover:bg-brown/5 dark:hover:bg-beige-medium/5 rounded-lg transition-colors"
        title="Property Request Notifications"
      >
        <BellIcon className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notifications Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-full mt-2 w-96 bg-white dark:bg-brown-dark rounded-lg shadow-xl border border-brown/10 dark:border-beige-medium/10 z-50 max-h-96 overflow-hidden"
            >
              {/* Header */}
              <div className="p-4 border-b border-brown/10 dark:border-beige-medium/10">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-heading font-semibold text-brown-dark dark:text-beige-light">
                    Notifications
                  </h3>
                  {unreadCount > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="text-sm text-taupe hover:text-taupe/80 transition-colors"
                    >
                      Mark all read
                    </button>
                  )}
                </div>
                {unreadCount > 0 && (
                  <p className="text-sm text-brown dark:text-beige-medium mt-1">
                    {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                  </p>
                )}
              </div>

              {/* Notifications List */}
              <div className="max-h-80 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center">
                    <BellIcon className="h-12 w-12 text-brown/30 dark:text-beige-medium/30 mx-auto mb-3" />
                    <p className="text-brown dark:text-beige-medium">No notifications yet</p>
                    <p className="text-sm text-brown/70 dark:text-beige-medium/70 mt-1">
                      You'll see new property requests here
                    </p>
                  </div>
                ) : (
                  <div className="divide-y divide-brown/10 dark:divide-beige-medium/10">
                    {notifications.map((notification) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        className={`p-4 hover:bg-brown/5 dark:hover:bg-beige-medium/5 cursor-pointer transition-colors ${
                          !notification.read ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''
                        }`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg ${
                            notification.type === 'new_request' 
                              ? 'bg-blue-100 dark:bg-blue-900/20' 
                              : 'bg-gray-100 dark:bg-gray-900/20'
                          }`}>
                            <HomeIcon className={`h-4 w-4 ${
                              notification.type === 'new_request'
                                ? 'text-blue-600 dark:text-blue-400'
                                : 'text-gray-600 dark:text-gray-400'
                            }`} />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-brown-dark dark:text-beige-light">
                                {notification.title}
                              </p>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeNotification(notification.id);
                                }}
                                className="text-brown/50 dark:text-beige-medium/50 hover:text-brown dark:hover:text-beige-medium transition-colors"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </div>
                            
                            <p className="text-sm text-brown dark:text-beige-medium mt-1">
                              {notification.message}
                            </p>
                            
                            {notification.data && (
                              <div className="flex items-center gap-4 mt-2 text-xs text-brown/70 dark:text-beige-medium/70">
                                <div className="flex items-center gap-1">
                                  <UserIcon className="h-3 w-3" />
                                  {notification.data.name}
                                </div>
                                <div className="flex items-center gap-1">
                                  <MapPinIcon className="h-3 w-3" />
                                  {notification.data.location_preference}
                                </div>
                              </div>
                            )}
                            
                            <div className="flex items-center justify-between mt-2">
                              <div className="flex items-center gap-1 text-xs text-brown/50 dark:text-beige-medium/50">
                                <ClockIcon className="h-3 w-3" />
                                {formatTimeAgo(notification.timestamp)}
                              </div>
                              {!notification.read && (
                                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PropertyRequestNotifications;
