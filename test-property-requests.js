/**
 * Test script to verify property request functionality
 * This script tests the database functions and API endpoints
 */

import { createClient } from "@supabase/supabase-js";

// Supabase configuration (using environment variables or defaults)
const supabaseUrl = "https://ityjoygbvbcvnxcwoqve.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0eWpveWdidmJjdm54Y3dvcXZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNjEzMjEsImV4cCI6MjA2NDYzNzMyMX0.doCPIJ6jTAa83oWs6kPB8P-pEAZ-tKg0UQIm_M7n6JY";

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testPropertyRequestFunctions() {
  console.log("🧪 Testing Property Request Functions...\n");

  try {
    // Test 1: Check if property_requests table exists
    console.log("1️⃣ Testing table existence...");
    const { data: tableCheck, error: tableError } = await supabase
      .from("property_requests")
      .select("count", { count: "exact", head: true });

    if (tableError) {
      console.error("❌ Table check failed:", tableError.message);
      return;
    }
    console.log("✅ property_requests table exists");

    // Test 2: Test get_property_request_stats function
    console.log("\n2️⃣ Testing get_property_request_stats function...");
    const { data: statsData, error: statsError } = await supabase.rpc(
      "get_property_request_stats"
    );

    if (statsError) {
      console.error("❌ Stats function failed:", statsError.message);
    } else {
      console.log("✅ get_property_request_stats function works");
      console.log("📊 Stats:", statsData);
    }

    // Test 3: Test get_property_requests function
    console.log("\n3️⃣ Testing get_property_requests function...");
    const { data: requestsData, error: requestsError } = await supabase.rpc(
      "get_property_requests",
      {
        p_limit: 5,
        p_offset: 0,
      }
    );

    if (requestsError) {
      console.error("❌ Requests function failed:", requestsError.message);
    } else {
      console.log("✅ get_property_requests function works");
      console.log("📋 Requests count:", requestsData?.length || 0);
    }

    // Test 4: Test creating a property request (this should work for anyone)
    console.log("\n4️⃣ Testing property request creation...");
    const testRequest = {
      name: "Test User",
      email: "<EMAIL>",
      phone: "+1234567890",
      property_type: "house",
      sale_type: "buy",
      location_preference: "Downtown",
      min_budget: 100000,
      max_budget: 500000,
      min_bedrooms: 2,
      max_bedrooms: 4,
      specific_features: ["garage", "garden"],
      additional_requirements: "Test request from automated script",
    };

    const { data: createData, error: createError } = await supabase
      .from("property_requests")
      .insert([testRequest])
      .select()
      .single();

    if (createError) {
      console.error("❌ Create request failed:", createError.message);
    } else {
      console.log("✅ Property request creation works");
      console.log("🆔 Created request ID:", createData.id);

      // Clean up: Delete the test request
      const { error: deleteError } = await supabase
        .from("property_requests")
        .delete()
        .eq("id", createData.id);

      if (deleteError) {
        console.warn(
          "⚠️ Failed to clean up test request:",
          deleteError.message
        );
      } else {
        console.log("🧹 Test request cleaned up");
      }
    }

    console.log("\n🎉 All tests completed!");
  } catch (error) {
    console.error("💥 Unexpected error:", error);
  }
}

// Run the tests
testPropertyRequestFunctions();
