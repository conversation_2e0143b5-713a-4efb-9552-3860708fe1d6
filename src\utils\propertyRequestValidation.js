/**
 * Property Request Validation Utilities
 * Comprehensive validation functions for property request forms
 */

/**
 * Validate email format
 * @param {string} email - Email address to validate
 * @returns {boolean} True if valid email format
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if valid phone format
 */
export const isValidPhone = (phone) => {
  if (!phone) return true; // Phone is optional
  const phoneRegex = /^[\+]?[\d\s\-\(\)\.]{7,20}$/;
  return phoneRegex.test(phone);
};

/**
 * Validate budget range
 * @param {number|string} minBudget - Minimum budget
 * @param {number|string} maxBudget - Maximum budget
 * @returns {Object} Validation result with isValid and error message
 */
export const validateBudgetRange = (minBudget, maxBudget) => {
  const min = parseFloat(minBudget) || 0;
  const max = parseFloat(maxBudget) || 0;

  if (min < 0) {
    return { isValid: false, error: 'Minimum budget cannot be negative' };
  }

  if (max < 0) {
    return { isValid: false, error: 'Maximum budget cannot be negative' };
  }

  if (min > 0 && max > 0 && min > max) {
    return { isValid: false, error: 'Minimum budget cannot be greater than maximum budget' };
  }

  if (min > 10000000) {
    return { isValid: false, error: 'Minimum budget seems unreasonably high' };
  }

  if (max > 50000000) {
    return { isValid: false, error: 'Maximum budget seems unreasonably high' };
  }

  return { isValid: true, error: null };
};

/**
 * Validate bedroom range
 * @param {number|string} minBedrooms - Minimum bedrooms
 * @param {number|string} maxBedrooms - Maximum bedrooms
 * @returns {Object} Validation result with isValid and error message
 */
export const validateBedroomRange = (minBedrooms, maxBedrooms) => {
  const min = parseInt(minBedrooms) || 0;
  const max = parseInt(maxBedrooms) || 0;

  if (min < 0) {
    return { isValid: false, error: 'Minimum bedrooms cannot be negative' };
  }

  if (max < 0) {
    return { isValid: false, error: 'Maximum bedrooms cannot be negative' };
  }

  if (min > 20) {
    return { isValid: false, error: 'Minimum bedrooms seems unreasonably high' };
  }

  if (max > 20) {
    return { isValid: false, error: 'Maximum bedrooms seems unreasonably high' };
  }

  if (min > 0 && max > 0 && min > max) {
    return { isValid: false, error: 'Minimum bedrooms cannot be greater than maximum bedrooms' };
  }

  return { isValid: true, error: null };
};

/**
 * Validate bathroom range
 * @param {number|string} minBathrooms - Minimum bathrooms
 * @param {number|string} maxBathrooms - Maximum bathrooms
 * @returns {Object} Validation result with isValid and error message
 */
export const validateBathroomRange = (minBathrooms, maxBathrooms) => {
  const min = parseInt(minBathrooms) || 0;
  const max = parseInt(maxBathrooms) || 0;

  if (min < 0) {
    return { isValid: false, error: 'Minimum bathrooms cannot be negative' };
  }

  if (max < 0) {
    return { isValid: false, error: 'Maximum bathrooms cannot be negative' };
  }

  if (min > 15) {
    return { isValid: false, error: 'Minimum bathrooms seems unreasonably high' };
  }

  if (max > 15) {
    return { isValid: false, error: 'Maximum bathrooms seems unreasonably high' };
  }

  if (min > 0 && max > 0 && min > max) {
    return { isValid: false, error: 'Minimum bathrooms cannot be greater than maximum bathrooms' };
  }

  return { isValid: true, error: null };
};

/**
 * Validate square feet range
 * @param {number|string} minSquareFeet - Minimum square feet
 * @param {number|string} maxSquareFeet - Maximum square feet
 * @returns {Object} Validation result with isValid and error message
 */
export const validateSquareFeetRange = (minSquareFeet, maxSquareFeet) => {
  const min = parseInt(minSquareFeet) || 0;
  const max = parseInt(maxSquareFeet) || 0;

  if (min < 0) {
    return { isValid: false, error: 'Minimum square feet cannot be negative' };
  }

  if (max < 0) {
    return { isValid: false, error: 'Maximum square feet cannot be negative' };
  }

  if (min > 50000) {
    return { isValid: false, error: 'Minimum square feet seems unreasonably high' };
  }

  if (max > 100000) {
    return { isValid: false, error: 'Maximum square feet seems unreasonably high' };
  }

  if (min > 0 && max > 0 && min > max) {
    return { isValid: false, error: 'Minimum square feet cannot be greater than maximum square feet' };
  }

  return { isValid: true, error: null };
};

/**
 * Validate property request form data
 * @param {Object} formData - Form data to validate
 * @returns {Object} Validation result with isValid, errors object, and sanitized data
 */
export const validatePropertyRequestForm = (formData) => {
  const errors = {};
  let isValid = true;

  // Required field validation
  if (!formData.name || formData.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters long';
    isValid = false;
  }

  if (!formData.email || !isValidEmail(formData.email)) {
    errors.email = 'Please enter a valid email address';
    isValid = false;
  }

  if (!formData.property_type) {
    errors.property_type = 'Please select a property type';
    isValid = false;
  }

  if (!formData.sale_type) {
    errors.sale_type = 'Please select whether you want to buy or rent';
    isValid = false;
  }

  if (!formData.location_preference || formData.location_preference.trim().length < 2) {
    errors.location_preference = 'Please specify your location preference';
    isValid = false;
  }

  // Optional field validation
  if (formData.phone && !isValidPhone(formData.phone)) {
    errors.phone = 'Please enter a valid phone number';
    isValid = false;
  }

  // Range validations
  const budgetValidation = validateBudgetRange(formData.min_budget, formData.max_budget);
  if (!budgetValidation.isValid) {
    errors.budget = budgetValidation.error;
    isValid = false;
  }

  const bedroomValidation = validateBedroomRange(formData.min_bedrooms, formData.max_bedrooms);
  if (!bedroomValidation.isValid) {
    errors.bedrooms = bedroomValidation.error;
    isValid = false;
  }

  const bathroomValidation = validateBathroomRange(formData.min_bathrooms, formData.max_bathrooms);
  if (!bathroomValidation.isValid) {
    errors.bathrooms = bathroomValidation.error;
    isValid = false;
  }

  const squareFeetValidation = validateSquareFeetRange(formData.min_square_feet, formData.max_square_feet);
  if (!squareFeetValidation.isValid) {
    errors.square_feet = squareFeetValidation.error;
    isValid = false;
  }

  // Additional requirements length check
  if (formData.additional_requirements && formData.additional_requirements.length > 2000) {
    errors.additional_requirements = 'Additional requirements must be less than 2000 characters';
    isValid = false;
  }

  // Sanitize data
  const sanitizedData = {
    name: formData.name?.trim(),
    email: formData.email?.trim().toLowerCase(),
    phone: formData.phone?.trim() || null,
    property_type: formData.property_type,
    sale_type: formData.sale_type,
    location_preference: formData.location_preference?.trim(),
    min_budget: formData.min_budget ? parseFloat(formData.min_budget) : null,
    max_budget: formData.max_budget ? parseFloat(formData.max_budget) : null,
    min_bedrooms: formData.min_bedrooms ? parseInt(formData.min_bedrooms) : 0,
    max_bedrooms: formData.max_bedrooms ? parseInt(formData.max_bedrooms) : null,
    min_bathrooms: formData.min_bathrooms ? parseInt(formData.min_bathrooms) : 0,
    max_bathrooms: formData.max_bathrooms ? parseInt(formData.max_bathrooms) : null,
    min_square_feet: formData.min_square_feet ? parseInt(formData.min_square_feet) : null,
    max_square_feet: formData.max_square_feet ? parseInt(formData.max_square_feet) : null,
    specific_features: Array.isArray(formData.specific_features) ? formData.specific_features : [],
    additional_requirements: formData.additional_requirements?.trim() || null
  };

  return {
    isValid,
    errors,
    sanitizedData
  };
};

/**
 * Get user-friendly error messages for common validation errors
 * @param {string} errorType - Type of error
 * @returns {string} User-friendly error message
 */
export const getErrorMessage = (errorType) => {
  const errorMessages = {
    NETWORK_ERROR: 'Unable to connect to our servers. Please check your internet connection and try again.',
    VALIDATION_ERROR: 'Please check your form inputs and correct any errors.',
    SERVER_ERROR: 'Something went wrong on our end. Please try again in a few moments.',
    RATE_LIMIT_ERROR: 'Too many requests. Please wait a moment before trying again.',
    TIMEOUT_ERROR: 'The request took too long to complete. Please try again.',
    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again or contact support if the problem persists.'
  };

  return errorMessages[errorType] || errorMessages.UNKNOWN_ERROR;
};

/**
 * Debounce function for real-time validation
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Format validation errors for display
 * @param {Object} errors - Errors object from validation
 * @returns {Array} Array of formatted error messages
 */
export const formatValidationErrors = (errors) => {
  return Object.entries(errors).map(([field, message]) => ({
    field,
    message,
    id: `${field}-error`
  }));
};
