-- Create property requests system for handling user property inquiries

-- Create property_requests table
CREATE TABLE IF NOT EXISTS property_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Contact Information
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  
  -- Property Requirements
  property_type TEXT NOT NULL CHECK (property_type IN ('house', 'apartment', 'condo', 'land', 'commercial', 'townhouse', 'other')),
  sale_type TEXT NOT NULL CHECK (sale_type IN ('rent', 'buy')),
  location_preference TEXT NOT NULL,
  min_budget NUMERIC,
  max_budget NUMERIC,
  min_bedrooms INTEGER DEFAULT 0,
  max_bedrooms INTEGER,
  min_bathrooms INTEGER DEFAULT 0,
  max_bathrooms INTEGER,
  min_square_feet INTEGER,
  max_square_feet INTEGER,
  
  -- Additional Requirements
  specific_features TEXT[], -- Array of feature preferences
  additional_requirements TEXT,
  
  -- Status and Management
  status TEXT DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  admin_notes TEXT,
  assigned_admin_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  resolved_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_property_requests_status ON property_requests(status);
CREATE INDEX IF NOT EXISTS idx_property_requests_created_at ON property_requests(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_property_requests_email ON property_requests(email);
CREATE INDEX IF NOT EXISTS idx_property_requests_property_type ON property_requests(property_type);
CREATE INDEX IF NOT EXISTS idx_property_requests_sale_type ON property_requests(sale_type);
CREATE INDEX IF NOT EXISTS idx_property_requests_assigned_admin ON property_requests(assigned_admin_id);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_property_requests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_property_requests_updated_at
  BEFORE UPDATE ON property_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_property_requests_updated_at();

-- Row Level Security (RLS) Policies
ALTER TABLE property_requests ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can create property requests (for public form submissions)
CREATE POLICY "Anyone can create property requests" ON property_requests
  FOR INSERT
  WITH CHECK (true);

-- Policy: Only admins can view all property requests
CREATE POLICY "Admins can view all property requests" ON property_requests
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.is_admin = true
    )
  );

-- Policy: Only admins can update property requests
CREATE POLICY "Admins can update property requests" ON property_requests
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.is_admin = true
    )
  );

-- Policy: Only admins can delete property requests
CREATE POLICY "Admins can delete property requests" ON property_requests
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.is_admin = true
    )
  );

-- Create function to get property requests with pagination and filtering
CREATE OR REPLACE FUNCTION get_property_requests(
  p_limit INTEGER DEFAULT 20,
  p_offset INTEGER DEFAULT 0,
  p_status TEXT DEFAULT NULL,
  p_property_type TEXT DEFAULT NULL,
  p_sale_type TEXT DEFAULT NULL,
  p_search TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  email TEXT,
  phone TEXT,
  property_type TEXT,
  sale_type TEXT,
  location_preference TEXT,
  min_budget NUMERIC,
  max_budget NUMERIC,
  min_bedrooms INTEGER,
  max_bedrooms INTEGER,
  min_bathrooms INTEGER,
  max_bathrooms INTEGER,
  min_square_feet INTEGER,
  max_square_feet INTEGER,
  specific_features TEXT[],
  additional_requirements TEXT,
  status TEXT,
  priority TEXT,
  admin_notes TEXT,
  assigned_admin_id UUID,
  assigned_admin_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  total_count BIGINT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH filtered_requests AS (
    SELECT 
      pr.*,
      au.email as assigned_admin_name,
      COUNT(*) OVER() as total_count
    FROM property_requests pr
    LEFT JOIN auth.users au ON pr.assigned_admin_id = au.id
    WHERE 
      (p_status IS NULL OR pr.status = p_status)
      AND (p_property_type IS NULL OR pr.property_type = p_property_type)
      AND (p_sale_type IS NULL OR pr.sale_type = p_sale_type)
      AND (
        p_search IS NULL OR 
        pr.name ILIKE '%' || p_search || '%' OR
        pr.email ILIKE '%' || p_search || '%' OR
        pr.location_preference ILIKE '%' || p_search || '%' OR
        pr.additional_requirements ILIKE '%' || p_search || '%'
      )
    ORDER BY 
      CASE pr.priority 
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'normal' THEN 3
        WHEN 'low' THEN 4
      END,
      pr.created_at DESC
    LIMIT p_limit
    OFFSET p_offset
  )
  SELECT 
    fr.id,
    fr.name,
    fr.email,
    fr.phone,
    fr.property_type,
    fr.sale_type,
    fr.location_preference,
    fr.min_budget,
    fr.max_budget,
    fr.min_bedrooms,
    fr.max_bedrooms,
    fr.min_bathrooms,
    fr.max_bathrooms,
    fr.min_square_feet,
    fr.max_square_feet,
    fr.specific_features,
    fr.additional_requirements,
    fr.status,
    fr.priority,
    fr.admin_notes,
    fr.assigned_admin_id,
    fr.assigned_admin_name,
    fr.created_at,
    fr.updated_at,
    fr.resolved_at,
    fr.total_count
  FROM filtered_requests fr;
END;
$$;

-- Create function to update property request status
CREATE OR REPLACE FUNCTION update_property_request_status(
  p_request_id UUID,
  p_status TEXT,
  p_admin_notes TEXT DEFAULT NULL,
  p_assigned_admin_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE property_requests 
  SET 
    status = p_status,
    admin_notes = COALESCE(p_admin_notes, admin_notes),
    assigned_admin_id = COALESCE(p_assigned_admin_id, assigned_admin_id),
    resolved_at = CASE WHEN p_status = 'resolved' THEN now() ELSE resolved_at END
  WHERE id = p_request_id;
  
  RETURN FOUND;
END;
$$;

-- Create function to get property request statistics
CREATE OR REPLACE FUNCTION get_property_request_stats()
RETURNS TABLE (
  total_requests BIGINT,
  new_requests BIGINT,
  in_progress_requests BIGINT,
  resolved_requests BIGINT,
  closed_requests BIGINT,
  urgent_requests BIGINT,
  requests_this_week BIGINT,
  requests_this_month BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) as total_requests,
    COUNT(*) FILTER (WHERE status = 'new') as new_requests,
    COUNT(*) FILTER (WHERE status = 'in_progress') as in_progress_requests,
    COUNT(*) FILTER (WHERE status = 'resolved') as resolved_requests,
    COUNT(*) FILTER (WHERE status = 'closed') as closed_requests,
    COUNT(*) FILTER (WHERE priority = 'urgent') as urgent_requests,
    COUNT(*) FILTER (WHERE created_at >= date_trunc('week', now())) as requests_this_week,
    COUNT(*) FILTER (WHERE created_at >= date_trunc('month', now())) as requests_this_month
  FROM property_requests;
END;
$$;

-- Create function to trigger email notification
CREATE OR REPLACE FUNCTION trigger_property_request_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Only trigger for new inserts
  IF TG_OP = 'INSERT' THEN
    -- Call the edge function asynchronously (fire and forget)
    PERFORM
      net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/send-property-request-notification',
        headers := jsonb_build_object(
          'Content-Type', 'application/json',
          'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
        ),
        body := jsonb_build_object('requestId', NEW.id)
      );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for email notifications
CREATE TRIGGER trigger_property_request_email_notification
  AFTER INSERT ON property_requests
  FOR EACH ROW
  EXECUTE FUNCTION trigger_property_request_notification();
