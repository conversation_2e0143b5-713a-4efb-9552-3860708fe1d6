import { serve } from "https://deno.land/std@0.208.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type, x-requested-with",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Max-Age": "86400",
};

// Simple rate limiter
class RateLimiter {
  private storage = new Map<string, { count: number; resetTime: number }>();

  constructor(private windowMs: number, private maxRequests: number) {}

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const record = this.storage.get(identifier);

    if (!record || now > record.resetTime) {
      this.storage.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs,
      });
      return true;
    }

    if (record.count >= this.maxRequests) {
      return false;
    }

    record.count++;
    return true;
  }
}

// Rate limiter: 5 requests per 15 minutes per IP
const rateLimiter = new RateLimiter(15 * 60 * 1000, 5);

interface PropertyRequest {
  id: string;
  name: string;
  email: string;
  phone?: string;
  property_type: string;
  sale_type: string;
  location_preference: string;
  min_budget?: number;
  max_budget?: number;
  min_bedrooms?: number;
  max_bedrooms?: number;
  min_bathrooms?: number;
  max_bathrooms?: number;
  min_square_feet?: number;
  max_square_feet?: number;
  specific_features?: string[];
  additional_requirements?: string;
  created_at: string;
}

// Email template for user confirmation
function generateUserConfirmationTemplate(request: PropertyRequest): string {
  const formatPropertyType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const formatSaleType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Request Confirmation - UrbanEdge Real Estate</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content { padding: 30px 20px; }
        .section { margin-bottom: 25px; }
        .highlight-box { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-left: 4px solid #8B4513; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .info-item { background-color: #f9f9f9; padding: 12px; border-radius: 6px; }
        .info-label { font-weight: 600; color: #666; font-size: 12px; text-transform: uppercase; margin-bottom: 4px; }
        .info-value { color: #333; font-size: 14px; }
        .cta { background-color: #8B4513; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; font-weight: 600; }
        .footer { background-color: #f9f9f9; padding: 20px; text-align: center; color: #666; font-size: 12px; }
        .contact-info { background-color: #fff8f0; border: 1px solid #8B4513; border-radius: 6px; padding: 15px; margin: 20px 0; }
        @media (max-width: 600px) { .info-grid { grid-template-columns: 1fr; } .container { margin: 0; border-radius: 0; } .content { padding: 20px 15px; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Request Received!</h1>
            <p>Thank you for choosing UrbanEdge Real Estate</p>
        </div>

        <div class="content">
            <div class="section">
                <h2 style="color: #8B4513; margin-bottom: 15px;">Hello ${
                  request.name
                },</h2>
                <p>We've successfully received your property request and our team is already working to find the perfect match for you.</p>
            </div>

            <div class="highlight-box">
                <h3 style="margin-top: 0; color: #8B4513;">Your Request Summary</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Property Type</div>
                        <div class="info-value">${formatPropertyType(
                          request.property_type
                        )}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Looking to</div>
                        <div class="info-value">${formatSaleType(
                          request.sale_type
                        )}</div>
                    </div>
                    <div class="info-item" style="grid-column: 1 / -1;">
                        <div class="info-label">Preferred Location</div>
                        <div class="info-value">${
                          request.location_preference
                        }</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3 style="color: #8B4513;">What Happens Next?</h3>
                <ul style="padding-left: 20px;">
                    <li><strong>Within 24 hours:</strong> Our team will review your requirements and search our database</li>
                    <li><strong>Within 48 hours:</strong> We'll contact you with initial property matches</li>
                    <li><strong>Ongoing:</strong> We'll continue to monitor new listings that match your criteria</li>
                </ul>
            </div>

            <div class="contact-info">
                <h4 style="margin-top: 0; color: #8B4513;">Need to Update Your Request?</h4>
                <p style="margin-bottom: 10px;">If you need to modify your requirements or have any questions, don't hesitate to reach out:</p>
                <p style="margin: 5px 0;"><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #8B4513;"><EMAIL></a></p>
                <p style="margin: 5px 0;"><strong>Phone:</strong> <a href="tel:+1234567890" style="color: #8B4513;">(*************</a></p>
            </div>
        </div>

        <div class="footer">
            <p><strong>UrbanEdge Real Estate</strong></p>
            <p>Your trusted partner in finding the perfect property</p>
        </div>
    </div>
</body>
</html>
  `;
}

// Email template for admin notification
function generateAdminEmailTemplate(request: PropertyRequest): string {
  const formatBudget = (amount?: number) => {
    if (!amount) return "Not specified";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatBudgetRange = (min?: number, max?: number) => {
    if (!min && !max) return "Not specified";
    if (!min) return `Up to ${formatBudget(max)}`;
    if (!max) return `From ${formatBudget(min)}`;
    return `${formatBudget(min)} - ${formatBudget(max)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Property Request - UrbanEdge Real Estate</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content { padding: 30px 20px; }
        .section { margin-bottom: 25px; }
        .section h2 { color: #8B4513; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #f0f0f0; padding-bottom: 8px; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
        .info-item { background-color: #f9f9f9; padding: 12px; border-radius: 6px; }
        .info-label { font-weight: 600; color: #666; font-size: 12px; text-transform: uppercase; margin-bottom: 4px; }
        .info-value { color: #333; font-size: 14px; }
        .features { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 10px; }
        .feature-tag { background-color: #8B4513; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; }
        .cta { background-color: #8B4513; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-top: 20px; font-weight: 600; }
        .footer { background-color: #f9f9f9; padding: 20px; text-align: center; color: #666; font-size: 12px; }
        @media (max-width: 600px) {
            .info-grid { grid-template-columns: 1fr; }
            .container { margin: 0; border-radius: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 New Property Request</h1>
            <p>A potential client has submitted a property request</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>👤 Contact Information</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Name</div>
                        <div class="info-value">${request.name}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email</div>
                        <div class="info-value"><a href="mailto:${
                          request.email
                        }" style="color: #8B4513;">${request.email}</a></div>
                    </div>
                    ${
                      request.phone
                        ? `
                    <div class="info-item">
                        <div class="info-label">Phone</div>
                        <div class="info-value"><a href="tel:${request.phone}" style="color: #8B4513;">${request.phone}</a></div>
                    </div>
                    `
                        : ""
                    }
                </div>
            </div>

            <div class="section">
                <h2>🏡 Property Requirements</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Property Type</div>
                        <div class="info-value">${
                          request.property_type.charAt(0).toUpperCase() +
                          request.property_type.slice(1)
                        }</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Looking to</div>
                        <div class="info-value">${
                          request.sale_type.charAt(0).toUpperCase() +
                          request.sale_type.slice(1)
                        }</div>
                    </div>
                    <div class="info-item" style="grid-column: 1 / -1;">
                        <div class="info-label">Location Preference</div>
                        <div class="info-value">${
                          request.location_preference
                        }</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>💰 Budget & Size</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Budget Range</div>
                        <div class="info-value">${formatBudgetRange(
                          request.min_budget,
                          request.max_budget
                        )}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bedrooms</div>
                        <div class="info-value">${
                          request.min_bedrooms || 0
                        } - ${request.max_bedrooms || "∞"}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bathrooms</div>
                        <div class="info-value">${
                          request.min_bathrooms || 0
                        } - ${request.max_bathrooms || "∞"}</div>
                    </div>
                    ${
                      request.min_square_feet || request.max_square_feet
                        ? `
                    <div class="info-item">
                        <div class="info-label">Square Feet</div>
                        <div class="info-value">${
                          request.min_square_feet || 0
                        } - ${request.max_square_feet || "∞"}</div>
                    </div>
                    `
                        : ""
                    }
                </div>
            </div>

            ${
              request.specific_features && request.specific_features.length > 0
                ? `
            <div class="section">
                <h2>✨ Desired Features</h2>
                <div class="features">
                    ${request.specific_features
                      .map(
                        (feature) =>
                          `<span class="feature-tag">${feature}</span>`
                      )
                      .join("")}
                </div>
            </div>
            `
                : ""
            }

            ${
              request.additional_requirements
                ? `
            <div class="section">
                <h2>📝 Additional Requirements</h2>
                <div style="background-color: #f9f9f9; padding: 15px; border-radius: 6px; white-space: pre-wrap;">${request.additional_requirements}</div>
            </div>
            `
                : ""
            }

            <div class="section">
                <h2>📅 Request Details</h2>
                <div class="info-item">
                    <div class="info-label">Submitted</div>
                    <div class="info-value">${formatDate(
                      request.created_at
                    )}</div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="${
                  Deno.env.get("ADMIN_DASHBOARD_URL") ||
                  "https://your-domain.com/admin/dashboard"
                }?tab=requests" class="cta">
                    View in Admin Dashboard
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>UrbanEdge Real Estate</strong></p>
            <p>This is an automated notification. Please respond to the client directly.</p>
        </div>
    </div>
</body>
</html>
  `;
}

// Send email using Resend
async function sendEmailWithResend(
  to: string,
  subject: string,
  html: string
): Promise<void> {
  const resendApiKey = Deno.env.get("RESEND_API_KEY");
  if (!resendApiKey) {
    throw new Error("RESEND_API_KEY environment variable is not set");
  }

  const response = await fetch("https://api.resend.com/emails", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${resendApiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      from: Deno.env.get("FROM_EMAIL") || "<EMAIL>",
      to: [to],
      subject: subject,
      html: html,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to send email: ${error}`);
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Rate limiting
    const clientIP = req.headers.get("x-forwarded-for") || "unknown";
    if (!rateLimiter.isAllowed(clientIP)) {
      return new Response(
        JSON.stringify({
          error: "Rate limit exceeded. Please try again later.",
        }),
        {
          status: 429,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Validate request method
    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Parse request body
    const { requestId } = await req.json();

    if (!requestId) {
      return new Response(JSON.stringify({ error: "Request ID is required" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Fetch the property request
    const { data: request, error: fetchError } = await supabase
      .from("property_requests")
      .select("*")
      .eq("id", requestId)
      .single();

    if (fetchError || !request) {
      return new Response(
        JSON.stringify({ error: "Property request not found" }),
        {
          status: 404,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Get admin email from environment or use default
    const adminEmail = Deno.env.get("ADMIN_EMAIL") || "<EMAIL>";

    // Generate admin email content
    const adminSubject = `New Property Request from ${request.name}`;
    const adminHtmlContent = generateAdminEmailTemplate(request);

    // Generate user confirmation email content
    const userSubject = `Property Request Confirmation - UrbanEdge Real Estate`;
    const userHtmlContent = generateUserConfirmationTemplate(request);

    // Send admin notification email
    await sendEmailWithResend(adminEmail, adminSubject, adminHtmlContent);

    // Send user confirmation email
    await sendEmailWithResend(request.email, userSubject, userHtmlContent);

    return new Response(
      JSON.stringify({
        success: true,
        message: "Notification sent successfully",
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error in send-property-request-notification:", error);

    return new Response(
      JSON.stringify({
        error: "Internal server error",
        message: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
