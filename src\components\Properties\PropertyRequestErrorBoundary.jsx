import React from 'react';
import { motion } from 'framer-motion';
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

/**
 * Error Boundary for Property Request Components
 * Catches and handles errors in the property request feature
 */
class PropertyRequestErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Property Request Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // You could also log the error to an error reporting service here
    // Example: logErrorToService(error, errorInfo);
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { error } = this.state;
      const { fallback: CustomFallback } = this.props;

      // If a custom fallback component is provided, use it
      if (CustomFallback) {
        return (
          <CustomFallback 
            error={error}
            onRetry={this.handleRetry}
            onReload={this.handleReload}
          />
        );
      }

      // Default error UI
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="min-h-[400px] flex items-center justify-center p-6"
        >
          <div className="text-center max-w-md mx-auto">
            <div className="mb-6">
              <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-heading font-bold text-brown-dark dark:text-beige-light mb-2">
                Something went wrong
              </h2>
              <p className="text-brown dark:text-beige-medium">
                We encountered an error while processing your property request. This has been logged and we're working to fix it.
              </p>
            </div>

            {/* Error details (only in development) */}
            {process.env.NODE_ENV === 'development' && error && (
              <details className="mb-6 text-left bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <summary className="cursor-pointer font-medium text-red-800 dark:text-red-400 mb-2">
                  Error Details (Development Only)
                </summary>
                <pre className="text-xs text-red-700 dark:text-red-300 overflow-auto">
                  {error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={this.handleRetry}
                className="flex items-center justify-center gap-2 px-6 py-3 bg-taupe text-white rounded-lg hover:bg-taupe/90 transition-colors font-medium"
              >
                <ArrowPathIcon className="h-4 w-4" />
                Try Again
              </button>
              
              <button
                onClick={this.handleReload}
                className="px-6 py-3 border border-brown/20 dark:border-beige-medium/20 text-brown dark:text-beige-medium rounded-lg hover:bg-brown/5 dark:hover:bg-beige-medium/5 transition-colors font-medium"
              >
                Reload Page
              </button>
            </div>

            <p className="text-sm text-brown/70 dark:text-beige-medium/70 mt-4">
              If the problem persists, please contact our support team.
            </p>
          </div>
        </motion.div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook to handle async errors in functional components
 * @returns {Function} Function to report errors to the error boundary
 */
export const useErrorHandler = () => {
  const [, setError] = React.useState();
  
  return React.useCallback((error) => {
    setError(() => {
      throw error;
    });
  }, []);
};

/**
 * Higher-order component to wrap components with error boundary
 * @param {React.Component} Component - Component to wrap
 * @param {Object} options - Options for error boundary
 * @returns {React.Component} Wrapped component
 */
export const withErrorBoundary = (Component, options = {}) => {
  const WrappedComponent = (props) => (
    <PropertyRequestErrorBoundary {...options}>
      <Component {...props} />
    </PropertyRequestErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

/**
 * Custom fallback component for form-specific errors
 */
export const PropertyRequestFormErrorFallback = ({ error, onRetry, onReload }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center"
  >
    <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
    <h3 className="text-lg font-heading font-semibold text-red-800 dark:text-red-400 mb-2">
      Form Error
    </h3>
    <p className="text-red-700 dark:text-red-300 mb-4">
      There was an error with the property request form. Please try submitting again.
    </p>
    <div className="flex gap-3 justify-center">
      <button
        onClick={onRetry}
        className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
      >
        <ArrowPathIcon className="h-4 w-4" />
        Retry
      </button>
      <button
        onClick={onReload}
        className="px-4 py-2 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors text-sm font-medium"
      >
        Reload
      </button>
    </div>
  </motion.div>
);

/**
 * Custom fallback component for table/list errors
 */
export const PropertyRequestTableErrorFallback = ({ error, onRetry, onReload }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-8 text-center"
  >
    <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
    <h3 className="text-xl font-heading font-semibold text-brown-dark dark:text-beige-light mb-2">
      Unable to Load Property Requests
    </h3>
    <p className="text-brown dark:text-beige-medium mb-6">
      We encountered an error while loading the property requests. This might be a temporary issue.
    </p>
    <div className="flex gap-3 justify-center">
      <button
        onClick={onRetry}
        className="flex items-center gap-2 px-6 py-3 bg-taupe text-white rounded-lg hover:bg-taupe/90 transition-colors font-medium"
      >
        <ArrowPathIcon className="h-4 w-4" />
        Try Again
      </button>
      <button
        onClick={onReload}
        className="px-6 py-3 border border-brown/20 dark:border-beige-medium/20 text-brown dark:text-beige-medium rounded-lg hover:bg-brown/5 dark:hover:bg-beige-medium/5 transition-colors font-medium"
      >
        Reload Page
      </button>
    </div>
  </motion.div>
);

export default PropertyRequestErrorBoundary;
